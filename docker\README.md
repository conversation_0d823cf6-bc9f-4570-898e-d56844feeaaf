# AI 聊天应用 Docker 配置文档

## 概述

此目录包含使用 Docker 容器构建和运行 AI 聊天应用（前端和后端）的 Docker 配置文件。[Docker Compose](https://docs.docker.com/compose/) 用于定义和管理多容器应用。

## 先决条件

- 已安装 Docker 引擎（例如 Docker Desktop）
- 已安装 Docker Compose（通常随 Docker Desktop 一起提供）

## 文件结构 (在此 `docker/` 目录下)

```
docker/
├── docker-compose.yml              # 主要的 Docker Compose 配置文件
├── backend_host/
│   ├── Dockerfile                  # 后端服务 Dockerfile
│   └── .env.example               # 后端环境变量配置模板
├── frontend/
│   ├── Dockerfile                  # 前端服务 Dockerfile
│   └── .env.example               # 前端环境变量配置模板
└── README.md                       # 本文档
```

**相关源代码目录**：
- `../backend_host/`: 后端源代码，包含 `.dockerignore` 文件
- `../frontend/`: 前端源代码，包含 `.dockerignore` 文件

## 服务

### 1. 后端 (`backend_host`)

- **源代码目录**: `../backend_host` (相对于 `docker-compose.yml`)
- **Dockerfile**: `docker/backend_host/Dockerfile`
- **描述**: 基于 Python FastAPI 的 AI 聊天应用后端，作为多AI平台(百炼、Coze、火山方舟)的API代理，核心包含FAQ筛选器Agent
- **技术栈**:
  - **Python 3.11 Slim**: 运行环境
  - **FastAPI**: Web框架
  - **uv**: Python包管理工具
  - **多阶段构建**: 依赖构建阶段 + 最终运行阶段
  - **清华源**: 加速包下载
- **镜像配置**:
  - **镜像名称**: `harbor.zulong.com/ai-assistant/kefu-backend:v0.1.8`
  - **容器名称**: `ai_kefu_backend_prod`
  - **端口映射**: `18000:8000` (宿主机:容器)
- **环境变量**:
  - **配置文件**: `docker/backend_host/.env` (从 `docker/backend_host/.env.example` 复制)
  - **主要配置**: API密钥、模型配置、日志级别等
  - **完整列表**: 参考 `docker/backend_host/.env.example`

### 2. 前端 (`frontend`)

- **源代码目录**: `../frontend` (相对于 `docker-compose.yml`)
- **Dockerfile**: `docker/frontend/Dockerfile`
- **描述**: 基于 React 的智能客服FAQ筛选应用前端，提供聊天用户界面
- **技术栈**:
  - **React 19.1.0**: 前端框架
  - **Node.js 20 Alpine**: 构建环境
  - **Nginx Alpine**: 静态文件服务器
  - **多阶段构建**: 构建阶段 + 生产阶段
  - **envsubst**: 运行时环境变量注入工具
- **镜像配置**:
  - **镜像名称**: `harbor.zulong.com/ai-assistant/kefu-frontend:v0.1.8`
  - **容器名称**: `ai_kefu_frontend_prod`
  - **端口映射**: `13000:80` (宿主机:容器)
- **配置管理**:
  - **开发环境**: 支持 `.env` 文件或 `public/config.js` 文件配置
  - **Docker环境**: 运行时通过 `docker/frontend/.env` 文件动态生成配置
- **部署模式**:
  - **联合部署**: 与后端在同一 Docker 网络，使用内部服务名通信
  - **分离部署**: 独立部署，通过环境变量配置外部后端地址
- **运行时特性**:
  - **配置注入**: 使用 `envsubst` 将环境变量注入到 `config.js` 文件
  - **热配置**: 支持修改配置后重启容器生效，无需重新构建镜像
  - **SPA路由**: 支持 React Router 单页应用路由
  - **静态文件优化**: Gzip压缩、缓存策略、安全头配置
  - **启动流程**: `entrypoint.sh` → 生成配置 → 启动Nginx

## 使用方法

以下所有命令都应在此 `docker/` 目录中运行。

1.  **创建环境文件**:
    首次运行前，您需要为前后端服务分别创建环境文件：

    **后端环境配置**:
    ```bash
    cp backend_host/.env.example backend_host/.env
    # 编辑 backend_host/.env 文件，填入API密钥等配置
    ```

    **前端环境配置**:
    ```bash
    cp frontend/.env.example frontend/.env
    # 编辑 frontend/.env 文件，配置API地址
    ```

    **部署场景配置**:
    - **联合部署**: 前端使用 `REACT_APP_API_BASE_URL=http://backend_host:8000/api/v1`
    - **分离部署**: 前端使用外部后端地址，如 `REACT_APP_API_BASE_URL=http://**********:18000/api/v1`

2.  **构建 Docker 镜像**:
    为所有服务构建镜像：
    ```bash
    docker compose build
    ```
    为特定服务构建镜像：
    ```bash
    docker compose build backend_host
    docker compose build frontend
    ```

3.  **启动应用服务**:
    **联合部署** - 启动前后端所有服务：
    ```bash
    # 前台运行（推荐用于调试）
    docker compose up

    # 后台运行
    docker compose up -d
    ```

    **分离部署** - 仅启动特定服务：
    ```bash
    # 仅启动后端
    docker compose up backend_host -d

    # 仅启动前端（需要先配置 FRONTEND_API_BASE_URL）
    docker compose up frontend -d
    ```

    **手动启动示例**：
    ```bash
    # 后端手动启动
    docker run -d --name ai_kefu_backend_manual_run -p 18000:8000 --env-file ./backend_host/.env harbor.zulong.com/ai-assistant/kefu-backend:v0.1.8

    # 前端手动启动
    docker run -d --name ai_kefu_frontend_manual_run -p 13000:80 --env-file ./frontend/.env harbor.zulong.com/ai-assistant/kefu-frontend:v0.1.8
    ```

4.  **查看日志**:
    如果服务在分离模式下运行，您可以查看它们的日志：
    ```bash
    docker compose logs -f
    ```
    查看特定服务的日志：
    ```bash
    docker compose logs -f backend_host
    ```

5.  **停止应用服务**:
    停止并移除 `docker-compose.yml` 中定义的容器、网络等：
    ```bash
    docker compose down
    ```
    仅停止服务而不移除它们：
    ```bash
    docker compose stop
    ```

## 访问应用

服务启动后的访问地址：

-   **前端用户界面**: `http://localhost:13000`
-   **后端 API**: `http://localhost:18000`
-   **API 文档**: `http://localhost:18000/docs` (FastAPI 自动生成的 Swagger 文档)

## 镜像管理

### 推送到Harbor仓库

```bash
# 构建并推送后端镜像
docker compose build backend_host
docker push harbor.zulong.com/ai-assistant/kefu-backend:v0.1.8

# 构建并推送前端镜像
docker compose build frontend
docker push harbor.zulong.com/ai-assistant/kefu-frontend:v0.1.8
```

### 版本管理

镜像版本在 `docker-compose.yml` 中统一管理，当前版本为 `v0.1.8`。升级时需要：
1. 更新 `docker-compose.yml` 中的版本号
2. 重新构建和推送镜像
3. 在部署环境中拉取新版本

## 故障排除

### 常见问题

1. **前端无法连接后端**
   ```bash
   # 检查前端配置
   docker exec ai_kefu_frontend_prod cat /usr/share/nginx/html/config.js

   # 检查网络连通性
   docker exec ai_kefu_frontend_prod ping backend_host
   ```

2. **后端API密钥配置错误**
   ```bash
   # 检查环境变量
   docker exec ai_kefu_backend_prod env | grep API_KEY

   # 查看后端日志
   docker compose logs -f backend_host
   ```

3. **构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a

   # 重新构建（不使用缓存）
   docker compose build --no-cache
   ```

### 调试技巧

1. **进入容器调试**
   ```bash
   # 进入后端容器
   docker exec -it ai_kefu_backend_prod /bin/bash

   # 进入前端容器
   docker exec -it ai_kefu_frontend_prod /bin/sh
   ```

2. **查看详细日志**
   ```bash
   # 查看所有服务日志
   docker compose logs -f

   # 查看特定服务日志
   docker compose logs -f backend_host
   docker compose logs -f frontend
   ```

3. **网络诊断**
   ```bash
   # 查看Docker网络
   docker network ls
   docker network inspect docker_ai-kefu-network

   # 测试服务间连通性
   docker exec ai_kefu_frontend_prod wget -qO- http://backend_host:8000/health
   ```
