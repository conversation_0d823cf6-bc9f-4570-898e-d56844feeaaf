/* FAQ录入系统样式 */

.recorder-page {
  width: 100%;
  max-width: 50vw;
  margin: 0 auto;
  padding: 20px;
  background: #f8fafc;
  min-height: calc(100vh - 80px);
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.page-header h1 {
  color: #2d3748;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  color: #718096;
  margin: 0;
  font-size: 16px;
}

.recorder-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.section h2 {
  color: #2d3748;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

/* 渠道和服务选择样式 */
.channel-service-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.selector-row {
  display: flex;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.selector-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selector-group label {
  font-weight: 500;
  color: #4a5568;
  font-size: 14px;
}

/* 渠道组样式 */
.channel-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 服务组样式 */
.service-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 通用复选框样式 */
.common-checkbox-wrapper {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.common-checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #4a5568;
  user-select: none;
}

.common-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #4299e1;
}

.checkbox-text {
  font-weight: 500;
}

/* 禁用状态样式 */
.faq-channel-selector.disabled {
  opacity: 0.5;
  pointer-events: none;
  background: #f7fafc;
  color: #a0aec0;
}

.channel-selector, .faq-service-selector {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  background: #fff;
  min-width: 120px;
  transition: all 0.2s ease;
}

.channel-selector:focus, .faq-service-selector:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}



/* 问答表单样式 */
.qa-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
}

.form-label.required::after {
  content: " *";
  color: #e53e3e;
}

.form-textarea, .form-input {
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s;
}

.form-textarea:focus, .form-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.input-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.char-count {
  font-size: 12px;
  color: #a0aec0;
}

.input-hint {
  font-size: 12px;
  color: #718096;
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
}

.analyze-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analyze-button.primary {
  background: #4299e1;
  color: #fff;
}

.analyze-button.primary:hover {
  background: #3182ce;
}

.analyze-button.disabled {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.form-tips {
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  border-left: 4px solid #38b2ac;
}

.tip-item {
  margin-bottom: 8px;
}

.tip-list {
  margin: 8px 0 0 16px;
  color: #4a5568;
}

.tip-list li {
  margin-bottom: 4px;
  font-size: 14px;
}

.form-status {
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
}

.status-ready {
  background: #f0fff4;
  color: #22543d;
  border: 1px solid #c6f6d5;
}

.status-incomplete {
  background: #fffaf0;
  color: #744210;
  border: 1px solid #fed7aa;
}

/* AI推荐样式 */
.ai-recommendations {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendations-header h3 {
  color: #2d3748;
  margin: 0 0 4px 0;
  font-size: 18px;
}

.header-description {
  color: #718096;
  margin: 0;
  font-size: 14px;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s;
}

.recommendation-item.high {
  border-left: 4px solid #48bb78;
}

.recommendation-item.medium {
  border-left: 4px solid #ed8936;
}

.recommendation-item.low {
  border-left: 4px solid #a0aec0;
}

.recommendation-item.selected {
  background: #ebf8ff;
  border-color: #4299e1;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.recommendation-rank {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rank-number {
  background: #edf2f7;
  color: #4a5568;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.confidence-badge {
  display: flex;
  align-items: center;
  gap: 8px;
}

.confidence-text {
  font-weight: 600;
  font-size: 14px;
}

.confidence-badge.high .confidence-text {
  color: #22543d;
}

.confidence-badge.medium .confidence-text {
  color: #744210;
}

.confidence-badge.low .confidence-text {
  color: #4a5568;
}

.confidence-bar {
  width: 60px;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.confidence-badge.high .confidence-fill {
  background: #48bb78;
}

.confidence-badge.medium .confidence-fill {
  background: #ed8936;
}

.confidence-badge.low .confidence-fill {
  background: #a0aec0;
}

.select-button {
  padding: 6px 12px;
  border: 1px solid #4299e1;
  background: #fff;
  color: #4299e1;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.select-button:hover {
  background: #4299e1;
  color: #fff;
}

.select-button.selected {
  background: #4299e1;
  color: #fff;
}

.path-breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.path-item {
  background: #edf2f7;
  color: #4a5568;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
}

.path-separator {
  color: #a0aec0;
  font-weight: bold;
}

.new-category-badge {
  background: #fef5e7;
  color: #744210;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.recommendation-reason {
  margin-bottom: 8px;
}

.reason-label {
  font-weight: 500;
  color: #4a5568;
  font-size: 13px;
}

.reason-text {
  color: #718096;
  font-size: 13px;
  margin-left: 4px;
}

.recommendations-footer {
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.confidence-legend {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.legend-title {
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
}

.legend-items {
  display: flex;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #718096;
}

.legend-color {
  width: 12px;
  height: 3px;
  border-radius: 2px;
}

.legend-item.high .legend-color {
  background: #48bb78;
}

.legend-item.medium .legend-color {
  background: #ed8936;
}

.legend-item.low .legend-color {
  background: #a0aec0;
}

.recommendations-tips small {
  color: #718096;
  font-size: 13px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #a0aec0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 分类选择器样式 */
.category-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 顶部工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
}

.refresh-section {
  display: flex;
  align-items: center;
}

.refresh-button {
  padding: 8px 16px;
  border: 1px solid #4299e1;
  background: #fff;
  color: #4299e1;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.refresh-button:hover:not(:disabled) {
  background: #4299e1;
  color: #fff;
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-button.refreshing {
  opacity: 0.7;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.search-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  box-sizing: border-box;
}

.clear-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.clear-search:hover {
  background-color: #f7fafc;
  color: #4a5568;
}

.current-selection {
  padding: 12px;
  background: #ebf8ff;
  border-radius: 8px;
  border: 1px solid #bee3f8;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.selection-label {
  font-weight: 500;
  color: #2b6cb0;
  font-size: 14px;
}

.clear-selection {
  background: none;
  border: none;
  color: #4299e1;
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
}

.selection-path {
  display: flex;
  align-items: center;
  gap: 8px;
}

.path-segment {
  background: #4299e1;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
}

.path-segment.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.path-segment.clickable:hover {
  background: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

.path-arrow {
  color: #2b6cb0;
  font-weight: bold;
}

.tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
}

.tree-node {
  margin: 4px 0;
}

.node-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
  cursor: pointer;
}

.node-content:hover {
  background: #f7fafc;
}

.node-content.selected {
  background: #ebf8ff;
  border: 1px solid #4299e1;
}

.node-content.recommended {
  background: #f0fff4;
  border: 1px solid #9ae6b4;
}

/* 虚拟节点样式 */
.node-content.virtual {
  background: #fef5e7;
  border: 2px dashed #ed8936;
  position: relative;
}

.node-content.virtual:hover {
  background: #fed7aa;
}

.node-content.virtual.selected {
  background: #fed7aa;
  border: 2px solid #ed8936;
}

/* AI推荐的虚拟节点样式 */
.node-content.ai-recommended {
  background: #e6fffa;
  border: 2px solid #38b2ac;
  position: relative;
}

.node-content.ai-recommended:hover {
  background: #b2f5ea;
}

.node-content.ai-recommended.selected {
  background: #b2f5ea;
  border: 2px solid #319795;
}

/* AI推荐节点的特殊标识 */
.node-content.ai-recommended::before {
  content: '🤖';
  position: absolute;
  top: -8px;
  right: -8px;
  background: #38b2ac;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 1;
}

/* 节点闪烁定位效果 - 纯CSS实现 */
.node-content[data-flash] {
  animation: nodeFlash 2s ease-out;
}

@keyframes nodeFlash {
  0% {
    background: #ffd700;
    border-color: #ff8c00;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    transform: scale(1.02);
  }
  50% {
    background: #fff3cd;
    border-color: #ffc107;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.4);
  }
  100% {
    background: inherit;
    border-color: inherit;
    box-shadow: inherit;
    transform: scale(1);
  }
}

.node-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
}

.node-icon {
  font-size: 14px;
}

.node-name {
  font-size: 14px;
  color: #2d3748;
}

.recommendation-star {
  background: #fef5e7;
  color: #744210;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.answer-count {
  color: #a0aec0;
  font-size: 12px;
}

.node-actions .select-button {
  font-size: 12px;
}

/* 虚拟节点特殊标记 */
.virtual-marker {
  color: #ed8936;
  font-weight: bold;
  font-size: 16px;
}

/* 节点名称编辑器 */
.node-name-editor {
  padding: 2px 6px;
  border: 1px solid #ed8936;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  color: #2d3748;
  min-width: 120px;
}

.node-name-editor:focus {
  outline: none;
  border-color: #dd6b20;
  box-shadow: 0 0 0 2px rgba(237, 137, 54, 0.2);
}

/* 虚拟节点操作按钮 */
.add-child-button {
  background: #48bb78;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 4px;
  transition: all 0.2s;
}

.add-child-button:hover {
  background: #38a169;
}

/* 树操作按钮区域样式 */
.tree-actions {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
  display: flex;
  gap: 8px;
  align-items: center;
}

.add-root-button {
  background: #4299e1;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.add-root-button:hover {
  background: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.expand-all-button, .collapse-all-button {
  background: #68d391;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.expand-all-button:hover {
  background: #48bb78;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.collapse-all-button {
  background: #a0aec0;
}

.collapse-all-button:hover {
  background: #718096;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edit-virtual-button, .remove-virtual-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 2px 4px;
  border-radius: 3px;
  transition: all 0.2s;
  margin-right: 4px;
}

.edit-virtual-button:hover {
  background: rgba(66, 153, 225, 0.1);
}

.remove-virtual-button:hover {
  background: rgba(229, 62, 62, 0.1);
  color: #e53e3e;
}

.recommendation-reason {
  margin-left: 40px;
  margin-top: 4px;
}

.recommendation-reason small {
  color: #744210;
  font-size: 12px;
}

.selector-help {
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  border-left: 4px solid #4299e1;
}

.help-title {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8px;
  font-size: 14px;
}

.help-list {
  margin: 0;
  padding-left: 16px;
  color: #4a5568;
}

.help-list li {
  margin-bottom: 4px;
  font-size: 13px;
}

.add-category-section {
  padding: 16px;
  background: #fffaf0;
  border-radius: 8px;
  border: 1px solid #fed7aa;
}

.add-category-header {
  margin-bottom: 8px;
  font-weight: 500;
  color: #744210;
  font-size: 14px;
}

.add-category-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.add-category-button {
  background: #ed8936;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  align-self: flex-start;
}

.add-category-button:hover {
  background: #dd6b20;
}

.add-category-hint {
  color: #744210;
  font-size: 12px;
}

/* 质量检查样式 */
.quality-check-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.overall-status {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
}

.overall-status.success {
  background: #f0fff4;
  border: 1px solid #c6f6d5;
}

.overall-status.warning {
  background: #fffaf0;
  border: 1px solid #fed7aa;
}

.overall-status.error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
}

.status-icon {
  font-size: 24px;
}

.status-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
}

.overall-status.success .status-text h3 {
  color: #22543d;
}

.overall-status.warning .status-text h3 {
  color: #744210;
}

.overall-status.error .status-text h3 {
  color: #742a2a;
}

.status-text p {
  margin: 0;
  font-size: 14px;
}

.overall-status.success .status-text p {
  color: #276749;
}

.overall-status.warning .status-text p {
  color: #975a16;
}

.overall-status.error .status-text p {
  color: #9b2c2c;
}

.check-section {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.section-icon {
  font-size: 18px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  color: #2d3748;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.issue-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 6px;
}

.issue-item.error {
  background: #fed7d7;
}

.issue-item.warning {
  background: #fffaf0;
}

.issue-item.duplicate {
  background: #ebf8ff;
}



.issue-icon {
  font-size: 16px;
  margin-top: 2px;
}

.issue-content {
  flex: 1;
}

.issue-text {
  margin: 0;
  font-size: 14px;
  color: #2d3748;
}

.duplicate-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.duplicate-info p {
  margin: 0;
  font-size: 13px;
}

.duplicate-suggestion {
  padding: 8px;
  background: #edf2f7;
  border-radius: 4px;
  font-size: 13px;
}

.suggestion-label {
  font-weight: 500;
  color: #4a5568;
}

.quality-summary {
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  border-top: 3px solid #4299e1;
}

.summary-stats {
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.stat-label {
  font-weight: 500;
  color: #4a5568;
}

.stat-value {
  color: #2d3748;
}

.summary-actions {
  padding: 12px;
  border-radius: 6px;
}

.action-recommendation {
  font-size: 14px;
}

.action-recommendation.success {
  background: #f0fff4;
  color: #22543d;
}

.action-recommendation.warning {
  background: #fffaf0;
  color: #744210;
}

.action-recommendation.error {
  background: #fed7d7;
  color: #742a2a;
}

/* 提交确认样式 */
.submit-confirmation {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.confirmation-header {
  text-align: center;
}

.confirmation-header h3 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 20px;
}

.confirmation-header p {
  margin: 0;
  color: #718096;
  font-size: 14px;
}

.confirmation-preview {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.preview-section {
  background: #fff;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  font-weight: 500;
  color: #2d3748;
  font-size: 16px;
}

.toggle-preview {
  background: none;
  border: none;
  color: #4299e1;
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
}

.preview-summary {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.summary-label {
  font-weight: 500;
  color: #4a5568;
  min-width: 80px;
}

.summary-value {
  color: #2d3748;
}

.category-path {
  background: #ebf8ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #bee3f8;
}

.preview-details {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
}

.detail-group {
  margin-bottom: 16px;
}

.detail-group h4 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 14px;
  font-weight: 500;
}

.detail-content {
  padding: 12px;
  background: #f7fafc;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  color: #2d3748;
}

.quality-status {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.status-header {
  margin-bottom: 12px;
}

.status-title {
  font-weight: 500;
  color: #2d3748;
  font-size: 16px;
}

.status-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.status-summary.success {
  background: #f0fff4;
  border: 1px solid #c6f6d5;
}

.status-summary.warning {
  background: #fffaf0;
  border: 1px solid #fed7aa;
}

.status-summary.error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
}

.status-summary .status-icon {
  font-size: 20px;
}

.status-summary .status-text {
  font-size: 14px;
}

.status-summary.success .status-text {
  color: #22543d;
}

.status-summary.warning .status-text {
  color: #744210;
}

.status-summary.error .status-text {
  color: #742a2a;
}

.quality-issues-summary {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.issue-count {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.issue-count.error {
  background: #fed7d7;
  color: #742a2a;
}

.issue-count.warning {
  background: #fffaf0;
  color: #744210;
}

.issue-count.duplicate {
  background: #ebf8ff;
  color: #2b6cb0;
}





.submit-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.submit-button, .cancel-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.submit-button.primary {
  background: #48bb78;
  color: #fff;
}

.submit-button.primary:hover:not(:disabled) {
  background: #38a169;
}

.submit-button.disabled {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
}

.cancel-button {
  background: #edf2f7;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.cancel-button:hover:not(:disabled) {
  background: #e2e8f0;
}

.submit-notes {
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  border-left: 4px solid #4299e1;
}

.note-title {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8px;
  font-size: 14px;
}

.note-list {
  margin: 0;
  padding-left: 16px;
  color: #4a5568;
}

.note-list li {
  margin-bottom: 4px;
  font-size: 13px;
}

.submit-result {
  padding: 24px;
  border-radius: 8px;
  text-align: center;
}

.submit-result.success {
  background: #f0fff4;
  border: 1px solid #c6f6d5;
}

.submit-result.error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
}

.result-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.result-content h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
}

.submit-result.success .result-content h3 {
  color: #22543d;
}

.submit-result.error .result-content h3 {
  color: #742a2a;
}

.result-message {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.submit-result.success .result-message {
  color: #276749;
}

.submit-result.error .result-message {
  color: #9b2c2c;
}

.success-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 16px 0;
  text-align: left;
}

.detail-item {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.detail-label {
  font-weight: 500;
  color: #2d3748;
  min-width: 80px;
}

.detail-value {
  color: #4a5568;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.action-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button.primary {
  background: #4299e1;
  color: #fff;
}

.action-button.primary:hover {
  background: #3182ce;
}

.action-button.secondary {
  background: #edf2f7;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.action-button.secondary:hover {
  background: #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recorder-page {
    padding: 12px;
  }
  
  .selector-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .recommendations-list .recommendation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .path-breadcrumb {
    flex-wrap: wrap;
  }
  
  .submit-actions {
    flex-direction: column;
  }
  
  .result-actions {
    flex-direction: column;
  }
}

/* 内容分析结果样式 */
.examine-results-section {
  margin-top: 20px;
}

.examine-results {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
}

.examine-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.examine-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 18px;
  font-weight: 600;
}

.confidence-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 80px;
}

.confidence-badge.confidence-high {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.confidence-badge.confidence-medium {
  background: #fed7aa;
  color: #744210;
  border: 1px solid #f6ad55;
}

.confidence-badge.confidence-low {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

.examine-content {
  padding: 20px;
}

.examine-section {
  margin-bottom: 20px;
}

.examine-section:last-child {
  margin-bottom: 0;
}

.examine-section h4 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.info-label {
  font-weight: 500;
  color: #4a5568;
  font-size: 14px;
  min-width: 80px;
}

.info-value {
  color: #2d3748;
  font-size: 14px;
  font-weight: 500;
}

.topic-domain {
  background: #ebf8ff;
  color: #2b6cb0;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #bee3f8;
}

.question-type {
  background: #f0fff4;
  color: #22543d;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #c6f6d5;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  background: #edf2f7;
  color: #4a5568;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

.scenarios-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.scenario-item {
  padding: 8px 12px;
  margin-bottom: 6px;
  background: #f7fafc;
  border-radius: 6px;
  border-left: 3px solid #4299e1;
  color: #2d3748;
  font-size: 14px;
}

.scenario-item:last-child {
  margin-bottom: 0;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-tag {
  background: #fffaf0;
  color: #744210;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #fed7aa;
}

.reasoning-content {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.reasoning-content p {
  margin: 0;
  color: #2d3748;
  font-size: 14px;
  line-height: 1.6;
}

/* 响应式设计 - 内容分析结果 */
@media (max-width: 768px) {
  .examine-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .keywords-list,
  .suggestions-list {
    flex-direction: column;
  }
  
  .keyword-tag,
  .suggestion-tag {
    text-align: center;
  }
}