"""
FAQ质量检测器

负责检测FAQ数据的质量问题，包括重复检测、分类冲突检测和格式验证。
"""

import re
import json
from typing import List, Dict, Any, Optional, Set, Tuple
import jieba
import traceback

from ai_app.models.faq_recorder import (
    FAQRecorderQuestionAnswer, 
    QualityCheckResult
)
from ai_app.services.faq_management.data_parser import FAQDataParser
from ai_app.shared.exceptions import QualityCheckError, DuplicateQuestionError, CategoryConflictError
from ai_app.shared.request_tracing import get_traced_logger
from ai_app.shared.quick_matcher import QuickMatcher

logger = get_traced_logger(__name__)


class FAQQualityChecker:
    """
    FAQ质量检测器
    
    功能：
    1. 检测重复问题
    2. 检测分类冲突
    3. 验证数据格式
    4. 评估内容质量
    """
    
    def __init__(self, faq_parser: FAQDataParser):
        """
        初始化质量检测器
        
        Args:
            faq_parser: FAQ数据解析器实例
        """
        self.faq_parser = faq_parser
        self.similarity_threshold = 0.9  # 相似度阈值
        self.min_answer_length = 5  # 最小答案长度
        self.max_answer_length = 2000  # 最大答案长度
        self.min_question_length = 3  # 最小问题长度
        self.max_question_length = 500  # 最大问题长度
        
        logger.debug("FAQQualityChecker initialized")
    
    def check_quality(
        self,
        qa_pair: FAQRecorderQuestionAnswer,
        category_path_list: List[List[str]],
    ) -> QualityCheckResult:
        """
        执行完整的质量检查
        
        Args:
            qa_pair: 问答对数据
            category_path_list: 分类路径列表
            
        Returns:
            质量检查结果
            
        Raises:
            QualityCheckError: 质量检查过程中发生错误
        """
        try:
            logger.info(f"Starting quality check for question: {qa_pair.question[:50]}...")
            
            warnings = []
            errors = []
            duplicate_questions = []
            
            # 1. 基础格式验证
            format_issues = self._check_format_validity(qa_pair)
            if format_issues['errors']:
                errors.extend(format_issues['errors'])
            if format_issues['warnings']:
                warnings.extend(format_issues['warnings'])

            # 2. 内容质量评估
            try:
                quality_issues = self._assess_content_quality(qa_pair)
                if quality_issues['errors']:
                    errors.extend(quality_issues['errors'])
                if quality_issues['warnings']:
                    warnings.extend(quality_issues['warnings'])
            except Exception as e:
                logger.warning(f"Content quality assessment failed: {e}")
                warnings.append("内容质量评估失败")
            
            # 3. 重复问题检测
            try:
                duplicates = self._detect_duplicate_questions(qa_pair)
                if duplicates:
                    duplicate_questions.extend(duplicates)
                    warnings.append(f"发现 {len(duplicates)} 个重复问题")
            except Exception as e:
                logger.warning(f"Duplicate detection failed: {e}")
                warnings.append("重复检测失败，请手动检查")

            # 4. 分类路径验证
            for category_path in category_path_list:
                try:
                    path_issues = self._validate_category_path(category_path)
                    if path_issues['errors']:
                        errors.extend(path_issues['errors'])
                    if path_issues['warnings']:
                        warnings.extend(path_issues['warnings'])
                except Exception as e:
                    logger.warning(f"Category path validation failed: {e}")
                    warnings.append("分类路径验证失败")
            
            # 5. 生成最终结果
            is_valid = len(errors) == 0
            
            result = QualityCheckResult(
                is_valid=is_valid,
                warnings=warnings,
                errors=errors,
                duplicate_questions=duplicate_questions,
            )
            
            logger.info(f"Quality check completed: valid={is_valid}, warnings={len(warnings)}, errors={len(errors)}")
            return result
            
        except Exception as e:
            logger.error(f"Quality check failed: {e}")
            raise QualityCheckError(f"Quality check failed: {e}") from e
    
    def _check_format_validity(self, qa_pair: FAQRecorderQuestionAnswer) -> Dict[str, List[str]]:
        """
        检查基础格式有效性
        
        Args:
            qa_pair: 问答对
            
        Returns:
            包含errors和warnings的字典
        """
        errors = []
        warnings = []
        
        # 检查问题
        if not qa_pair.question or not qa_pair.question.strip():
            errors.append("问题不能为空")
        else:
            question = qa_pair.question.strip()
            if len(question) < self.min_question_length:
                errors.append(f"问题过短，至少需要{self.min_question_length}个字符")
            elif len(question) > self.max_question_length:
                errors.append(f"问题过长，最多{self.max_question_length}个字符")
            
            # 检查问题格式
            if not self._has_question_markers(question):
                warnings.append("问题可能缺少疑问标记（？、吗、呢等）")
        
        # 检查答案
        if not qa_pair.answer or not qa_pair.answer.strip():
            errors.append("答案不能为空")
        else:
            answer = qa_pair.answer.strip()
            if len(answer) < self.min_answer_length:
                errors.append(f"答案过短，至少需要{self.min_answer_length}个字符")
            elif len(answer) > self.max_answer_length:
                warnings.append(f"答案较长({len(answer)}字符)，建议控制在{self.max_answer_length}字符以内")
            
            # 检查答案质量
            if self._is_low_quality_answer(answer):
                warnings.append("答案质量可能较低，建议提供更详细的说明")
        
        # 检查问题示例
        if qa_pair.question_example:
            if len(qa_pair.question_example.strip()) > self.max_question_length:
                warnings.append("问题示例过长")
        
        return {"errors": errors, "warnings": warnings}
    
    def _detect_duplicate_questions(
        self, 
        qa_pair: FAQRecorderQuestionAnswer
    ) -> List[Dict[str, Any]]:
        """
        检测重复问题
        
        Args:
            qa_pair: 问答对
            
        Returns:
            重复问题信息列表
        """
        duplicates = []
        question = qa_pair.question.lower().strip()
        
        try:
            # 获取所有现有问题
            all_questions = []
            self.faq_parser.collect_answers_recursively(all_questions)
            
            for existing_question_info in all_questions:
                existing_question = existing_question_info['question_example'].lower().strip()
                
                # 计算相似度
                quick_matcher = QuickMatcher()
                similarity = quick_matcher.calculate_similarity(question, existing_question)
                
                if similarity >= self.similarity_threshold:
                    duplicate_info = {
                        "existing_question": existing_question_info['question_example'],
                        "existing_answer": existing_question_info['answer'],
                        "existing_category": existing_question_info['desc_path'],
                        "similarity": similarity,
                        "key_path": existing_question_info.get('key_path', '')
                    }
                    duplicates.append(duplicate_info)
            
            logger.debug(f"Found {len(duplicates)} potential duplicates")
            return duplicates
            
        except Exception as e:
            logger.error(f"Error in duplicate detection: {e}\n{traceback.format_exc()}")
            #logger.error(json.dumps(all_questions, ensure_ascii=False, indent=2))
            return []
    
    def _detect_category_conflicts(
        self, 
        qa_pair: FAQRecorderQuestionAnswer,
        category_path: List[str]
    ) -> List[Dict[str, Any]]:
        """
        检测分类冲突
        
        Args:
            qa_pair: 问答对
            category_path: 目标分类路径
            
        Returns:
            分类冲突信息列表
        """
        conflicts = []
        
        try:
            # 查找相似问题在其他分类中的分布
            similar_questions = self._find_similar_questions_in_other_categories(
                qa_pair.question, category_path
            )
            
            for similar_info in similar_questions:
                if similar_info['similarity'] > 0.7:  # 高相似度阈值
                    conflict_info = {
                        "similar_question": similar_info['question'],
                        "existing_category": similar_info['category_path'],
                        "target_category": category_path,
                        "similarity": similar_info['similarity'],
                        "reason": "相似问题存在于不同分类中"
                    }
                    conflicts.append(conflict_info)
            
            logger.debug(f"Found {len(conflicts)} category conflicts")
            return conflicts
            
        except Exception as e:
            logger.error(f"Error in category conflict detection: {e}")
            return []
    
    def _assess_content_quality(
        self, 
        qa_pair: FAQRecorderQuestionAnswer,
    ) -> Dict[str, List[str]]:
        """
        评估内容质量
        
        Args:
            qa_pair: 问答对
            
        Returns:
            包含errors和warnings的字典
        """
        errors = []
        warnings = []
        
        question = qa_pair.question.strip()
        answer = qa_pair.answer.strip()
        
        # 检查问题质量
        if self._contains_inappropriate_content(question):
            errors.append("问题包含不当内容")
        
        if self._is_too_generic(question):
            warnings.append("问题过于宽泛，建议更具体")
        
        if self._contains_typos(question):
            warnings.append("问题可能包含拼写错误")
        
        # 检查答案质量
        if self._contains_inappropriate_content(answer):
            errors.append("答案包含不当内容")
        
        if self._lacks_actionable_information(answer):
            warnings.append("答案缺少可操作的信息")
        
        # 检查问答一致性
        if not self._is_answer_relevant_to_question(question, answer):
            warnings.append("答案与问题不匹配")
        
        return {"errors": errors, "warnings": warnings}
    
    def _validate_category_path(self, category_path: List[str]) -> Dict[str, List[str]]:
        """
        验证分类路径
        
        Args:
            category_path: 分类路径
            
        Returns:
            包含errors和warnings的字典
        """
        errors = []
        warnings = []
        
        if not category_path:
            errors.append("分类路径不能为空")
            return {"errors": errors, "warnings": warnings}
        
        category_path_str = FAQDataParser.description_path_to_string(category_path)

        if len(category_path) > 5:
            errors.append(f"'{category_path_str}'的分类层级过深（{len(category_path)}层），最多支持5层")

        # 检查是否有重复的分类名称
        if len(set(category_path)) < len(category_path):
            errors.append(f"'{category_path_str}'的分类名称重复")

        # 检查每个分类名称
        for i, category in enumerate(category_path):
            if not category or not category.strip():
                errors.append(f"'{category_path_str}'的第{i+1}级分类名称不能为空")
            else:
                category_name = category.strip()
                if len(category_name) > 50:
                    warnings.append(f"'{category_path_str}'的第{i+1}级分类名称过长（{len(category_name)}字符）")
                
                if not self._is_appropriate_category_name(category_name):
                    warnings.append(f"'{category_path_str}'的第{i+1}级分类名称可能不合适：{category_name}")
        
        return {"errors": errors, "warnings": warnings}
    
    def _has_question_markers(self, question: str) -> bool:
        """检查问题是否包含疑问标记"""
        question_markers = ['？', '?', '吗', '呢', '么', '如何', '怎么', '什么', '哪里', '我要',
        '何时', '谁', '是否', '有没有', '可以', '能 ', '能否', '应该', '会', '会否', '问题', 'bug', '异常']
        question_lower = question.lower()
        return any(marker in question_lower for marker in question_markers)
    
    def _is_low_quality_answer(self, answer: str) -> bool:
        """检查是否为低质量答案"""
        low_quality_patterns = [
            r'^(不知道|不清楚|不了解)$',
            r'^(好的|ok|嗯|哦)$',
            r'^(.)\1{5,}$',  # 重复字符
            r'^[0-9]+$'  # 纯数字
        ]
        
        answer_lower = answer.lower().strip()
        return any(re.match(pattern, answer_lower) for pattern in low_quality_patterns)
    
    def _contains_inappropriate_content(self, text: str) -> bool:
        """检查是否包含不当内容"""
        inappropriate_keywords = [
            '垃圾', '骗子', '傻逼', '白痴', '智障', 
            # 可以添加更多不当词汇
        ]
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in inappropriate_keywords)
    
    def _is_too_generic(self, question: str) -> bool:
        """检查问题是否过于宽泛"""
        generic_patterns = [
            r'^(怎么办|如何|什么)$',
            r'^(有什么|可以.{0,5}吗)$',
            r'^(帮助|帮忙)$'
        ]
        return any(re.match(pattern, question.strip()) for pattern in generic_patterns)
    
    def _contains_typos(self, text: str) -> bool:
        """简单的错别字检测"""
        # 这里可以实现更复杂的错别字检测逻辑
        common_typos = {
            '登陆': '登录',
            '帐号': '账号',
            '秘码': '密码'
        }
        return any(typo in text for typo in common_typos.keys())
    
    def _is_inconsistent_with_category(self, answer: str, category_path: List[str]) -> bool:
        """检查答案是否与分类不一致"""
        # 简单的一致性检查
        category_keywords = [cat.lower() for cat in category_path]
        answer_lower = answer.lower()
        
        # 如果答案完全不包含分类相关词汇，可能不一致
        return not any(keyword in answer_lower for keyword in category_keywords if len(keyword) > 1)
    
    def _lacks_actionable_information(self, answer: str) -> bool:
        """检查答案是否缺少可操作信息"""
        actionable_keywords = [
            '点击', '选择', '输入', '填写', '联系', '访问', '下载', '安装',
            '操作', '设置', '配置', '步骤', '方法', '流程'
        ]
        answer_lower = answer.lower()
        return not any(keyword in answer_lower for keyword in actionable_keywords)
    
    def _is_answer_relevant_to_question(self, question: str, answer: str) -> bool:
        """检查答案是否与问题相关"""
        # 简单的相关性检查
        question_keywords = set(jieba.cut(question.lower()))
        answer_keywords = set(jieba.cut(answer.lower()))
        
        # 计算关键词重叠度
        if not question_keywords:
            return True
        
        overlap = len(question_keywords & answer_keywords)
        overlap_ratio = overlap / len(question_keywords)
        
        return overlap_ratio > 0.1  # 至少10%的关键词重叠
    
    def _is_appropriate_category_name(self, category_name: str) -> bool:
        """检查分类名称是否合适"""
        inappropriate_chars = ['<', '>', '/', '\\', '|', '*', '?', '"', ':']
        return not any(char in category_name for char in inappropriate_chars)
