from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from ai_app.models.chat import ChatResponse

# 后端返回给前端的结构
class ChatToFaqFilterCandidate(BaseModel):
    """单个候选回复的结构"""
    content: str = Field(..., description="根据分类结果返回的参考答案。分类失败时会包含一个<保底答案>")
    category_chain: str = Field(..., description="问题分类链条，表示上述答案的分类路径。分类失败时字符串为空")
    score: Optional[float] = Field(None, description="此参考答案的置信度得分（如果可用，取值范围0~1）")
    reason: Optional[str] = Field(None, description="AI归为此类的原因依据（如果可用）")


class ChatToFaqFilterResponse(ChatResponse):
    """后端返回给前端的聊天响应体"""
    response_body: Optional[List[ChatToFaqFilterCandidate]] = Field(None, description="包含AI候选回复的列表，优先级从高到低")
    rewritten_query: Optional[str] = Field(None, description="重写后的玩家问题说明")
    classify_thinking: Optional[str] = Field(None, description="分类思考过程（如果可用）")