import React from 'react';
import ExamineResults from './ExamineResults';

/**
 * 问答输入表单组件
 * @param {Object} props
 * @param {Object} props.qaData - 问答数据对象 {question, answer, questionExample}
 * @param {Function} props.onChange - 数据变化回调
 * @param {Function} props.onAnalyze - 分析按钮点击回调
 * @param {boolean} props.isAnalyzing - 是否正在分析中
 * @param {Object} props.examineResult - 内容分析结果
 */
function QuestionAnswerForm({ qaData, onChange, onAnalyze, isAnalyzing, examineResult }) {
  
  // 处理输入变化
  const handleInputChange = (field, value) => {
    onChange({
      ...qaData,
      [field]: value
    });
  };

  // 检查是否可以进行分析
  const canAnalyze = qaData.question.trim() && qaData.answer.trim() && !isAnalyzing;

  return (
    <div className="qa-form">
      <div className="form-group">
        <label htmlFor="question-input" className="form-label required">
          用户问题
        </label>
        <textarea
          id="question-input"
          className="form-textarea question-input"
          placeholder="请输入用户可能提出的问题，例如：我忘记密码了怎么办？"
          value={qaData.question}
          onChange={(e) => handleInputChange('question', e.target.value)}
          rows={3}
          maxLength={500}
        />
        <div className="input-meta">
          <span className="char-count">{qaData.question.length}/500</span>
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="answer-input" className="form-label required">
          标准答案
        </label>
        <textarea
          id="answer-input"
          className="form-textarea answer-input"
          placeholder="请输入对应的标准答案，内容要准确、完整、易懂..."
          value={qaData.answer}
          onChange={(e) => handleInputChange('answer', e.target.value)}
          rows={5}
          maxLength={2000}
        />
        <div className="input-meta">
          <span className="char-count">{qaData.answer.length}/2000</span>
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="example-input" className="form-label">
          问题示例 (可选)
        </label>
        <input
          id="example-input"
          type="text"
          className="form-input example-input"
          placeholder="可以提供其他相似的问法，帮助AI更好地理解问题意图"
          value={qaData.questionExample}
          onChange={(e) => handleInputChange('questionExample', e.target.value)}
          maxLength={200}
        />
        <div className="input-meta">
          <span className="char-count">{qaData.questionExample.length}/200</span>
          <small className="input-hint">
            💡 提供相似问法可以提高AI分类的准确性
          </small>
        </div>
      </div>

      <div className="form-actions">
        <button
          type="button"
          className={`analyze-button ${canAnalyze ? 'primary' : 'disabled'}`}
          onClick={onAnalyze}
          disabled={!canAnalyze}
        >
          {isAnalyzing ? (
            <>
              <span className="loading-spinner"></span>
              🤖 AI分析中...
            </>
          ) : (
            <>
              🤖 开始AI分析
            </>
          )}
        </button>
        
        <div className="form-tips">
          <div className="tip-item">
            <strong>📝 填写提示：</strong>
          </div>
          <ul className="tip-list">
            <li>问题要表达清楚用户的实际需求</li>
            <li>答案要完整、准确，包含具体的操作步骤</li>
            <li>问题示例可以帮助AI更好地理解分类</li>
          </ul>
        </div>
      </div>

      {/* 表单状态指示 */}
      <div className="form-status">
        {qaData.question.trim() && qaData.answer.trim() ? (
          <div className="status-ready">
            ✅ 表单已填写完整，可以进行AI分析
          </div>
        ) : (
          <div className="status-incomplete">
            ⏳ 请填写必填项（问题和答案）后再进行分析
          </div>
        )}
      </div>

      {/* 内容分析结果展示 */}
      {examineResult && (
        <div className="examine-results-section">
          <ExamineResults examineResult={examineResult} />
        </div>
      )}
    </div>
  );
}

export default QuestionAnswerForm;