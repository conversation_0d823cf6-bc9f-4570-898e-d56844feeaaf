import logging
import logging.config # 确保导入
import argparse
import uvicorn
from contextlib import asynccontextmanager
from ai_app.server.routers import hello
from ai_app.server.routers import chat
from ai_app.server.routers import chat_to_faq_filter
from ai_app.server.routers import faq_recorder
from ai_app.config import config, get_logging_config
from ai_app.shared.request_tracing import RequestTracingMiddleware
from ai_app.services.faq_management.faq_repo_manager import initialize_faq_repo_manager, cleanup_faq_repo_manager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 注意：logging.config.dictConfig(get_logging_config()) 已在 config.py 模块加载时执行
# 因此，在 FastAPI() 实例化之前，基于环境变量的日志配置已经生效。

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化FAQ仓库管理器
    try:
        faq_repo_manager = initialize_faq_repo_manager(config.faq_excel_path)
    except Exception as e:
        raise RuntimeError(f"FAQ repo manager initialization failed: {e}")

    yield

    # 清理资源
    cleanup_faq_repo_manager()


app = FastAPI(lifespan=lifespan)

# --- 配置 CORS ---
# 允许所有来源，所有方法，所有头，可以根据需要调整
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], # 生产环境中应指定允许的源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["x-request-id"], # 明确暴露 x-request-id 头给前端
)

# --- 配置请求追踪中间件 ---
# 在CORS中间件之后添加，确保请求追踪功能正常工作
app.add_middleware(RequestTracingMiddleware)

# --- 包含路由 ---
api_prefix = "/api/v1"
app.include_router(chat_to_faq_filter.router, prefix=f"{api_prefix}")
app.include_router(faq_recorder.router, prefix=f"{api_prefix}")
app.include_router(hello.router, prefix=f"{api_prefix}", include_in_schema=False)
app.include_router(chat.router, prefix=f"{api_prefix}", include_in_schema=False)

# --- 主程序入口 ---
if __name__ == '__main__':
    # 设置命令行参数解析器
    parser = argparse.ArgumentParser(description='启动 FastAPI 后端服务')
    parser.add_argument('-v', '--verbose', action='store_true', help='启用 DEBUG 级别的日志记录')
    # 使用导入的 config 获取默认值
    parser.add_argument('--host', type=str, default=config.fastapi_host, help='服务监听的主机地址')
    parser.add_argument('--port', type=int, default=config.fastapi_port, help='服务监听的端口')
    parser.add_argument('--reload', action='store_true', help='启用热重载模式 (用于开发)')
    args = parser.parse_args()

    # 根据命令行参数（如果适用）重新配置日志
    # get_logging_config 内部会处理环境变量优先的逻辑
    logging.config.dictConfig(get_logging_config(cli_verbose_flag=args.verbose))

    # 移除旧的 LOGGING_CONFIG 定义和 logging.basicConfig

    logging.info(f"FastAPI 服务器准备启动，监听地址 {args.host}:{args.port}, 热重载: {args.reload}")
    # 日志级别信息现在由 get_logging_config 内部打印

    # 使用 uvicorn 启动应用
    uvicorn.run(
        app, # 直接传递在当前文件中定义的 FastAPI 实例 `app`
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_config=None, # 关键：我们已手动配置全局日志，Uvicorn 不再需要管理日志配置
        #loop="asyncio", # 强制使用 asyncio 事件循环。
                        # 测试发现默认的uvloop如果在docker desktop生成镜像，在centos7导入执行
                        # 可能会崩溃，猜测原因是uvloop采用cython的asyncio实现，跨平台可能不兼容
    )