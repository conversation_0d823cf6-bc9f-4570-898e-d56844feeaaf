/* 聊天相关样式已移至独立的chat.css文件 */

.chat-container {
  width: 66vw;
  max-width: 90vw;
  height: 66vh;
  max-height: 90vh;
  border: none;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08), 0 1.5px 4px rgba(0,0,0,0.03);
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow: hidden;
}

.messages {
  flex: 1;
  padding: 20px 18px 16px 18px;
  overflow-y: auto;
  background: #f8fafc;
}

.message-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}
.message-row.user {
  flex-direction: row-reverse;
}
.message-row.ai {
  flex-direction: row;
}
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin: 0 8px;
  background: #e9ecef;
}
.user-avatar {
  background: linear-gradient(135deg, #007bff 60%, #5bc0eb 100%);
  color: #fff;
}
.ai-avatar {
  background: #e9ecef;
  color: #007bff;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.error-avatar {
  background: #fff3cd;
  color: #d39e00;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1.5px solid #ffeeba;
}
.message {
  padding: 10px 15px;
  border-radius: 18px;
  margin-bottom: 5px;
  max-width: 70%;
  position: relative;
}
.message.user {
  background: linear-gradient(135deg, #007bff 60%, #5bc0eb 100%);
  color: #fff;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 18px;
}
.message.ai {
  background-color: #e9e9eb;
  color: #333;
  align-self: flex-start;
  margin-left: 10px;
  white-space: pre-wrap;
}
.message.error {
  align-self: center;
  background-color: #dc3545;
  color: white;
  font-weight: bold;
}
.input-area {
  display: flex;
  padding: 15px;
  border-top: 1px solid #eee;
  background-color: #fff;
  align-items: center;
}
.input-area input[type="text"] {
  flex-grow: 1;
  padding: 10px 15px;
  border: 1px solid #ccc;
  border-radius: 20px;
  margin-right: 10px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;
}
.input-area input[type="text"]:focus {
  border-color: #007bff;
}
.input-area input[type="text"]:disabled {
  background-color: #f8f9fa; /*  हल्का ग्रे बैकग्राउंड */
}

/* 新增：发送按钮和 Service 下拉框的容器 */
.send-service-group {
  display: flex;
  align-items: center;
  border: 1px solid #ccc; /* 统一边框 */
  border-radius: 20px; /* 统一圆角 */
  overflow: hidden; /* 隐藏内部元素的超出部分，确保圆角效果 */
  height: 42px; /* 与输入框高度一致 */
  margin-left: 5px; /* 与输入框的间距 */
  transition: border-color 0.2s;
}

.send-service-group:focus-within {
  border-color: #007bff; /* 获得焦点时边框变蓝 */
}

/* 调整组内发送按钮样式 */
.send-service-group .send-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0 12px;
  height: 100%; /* 占满容器高度 */
  border-radius: 0; /* 移除独立圆角 */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  border-right: 1px solid rgba(255, 255, 255, 0.3); /* 添加分隔线 */
}

.send-service-group .send-btn:hover {
  background-color: #0056b3;
}

.send-service-group .send-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  border-right-color: rgba(0, 0, 0, 0.1);
}

.send-service-group .send-btn:disabled svg {
  color: #888; /* 禁用时图标颜色 */
}

/* 调整组内 Service 下拉框样式 */
.send-service-group .service-select {
  background-color: #6c757d;
  color: white;
  border: none;
  height: 100%; /* 占满容器高度 */
  border-radius: 0; /* 移除独立圆角 */
  padding: 0 10px;
  font-size: 0.9rem;
  cursor: pointer;
  min-width: 90px;
  outline: none; /* 移除默认 outline */
  transition: background-color 0.2s;
}

.send-service-group .service-select:hover {
  background-color: #5a6268;
}

.send-service-group .service-select:disabled {
  background-color: #cccccc;
  color: #888;
  cursor: not-allowed;
}

/* 调整单独的清空按钮样式 */
.clear-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 50%; /* 圆形按钮 */
  width: 42px; /* 固定宽高使其为圆形 */
  height: 42px;
  padding: 0;
  margin-left: 10px; /* 与按钮组的间距 */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.clear-btn:hover {
  background-color: #c82333;
}

.clear-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.clear-btn:disabled svg {
  color: #888;
}

.send-btn svg,
.clear-btn svg {
  width: 20px;
  height: 20px;
}

@media (max-width: 500px) {
  .chat-container {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    box-shadow: none;
  }
  .messages {
    padding: 12px 4px 10px 4px;
  }
  .input-area {
    padding: 8px 4px;
  }
}

.message-footer {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 0.75em;
  color: #888;
  min-height: 1em;
  width: auto;
  padding: 0 4px;
}

.session-info-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin-right: 8px;
  cursor: pointer;
}

.session-toggle-icon {
  font-size: 0.9em;
  color: #aaa;
  display: inline-block;
  user-select: none;
}

.session-details-bubble {
  position: absolute;
  top: 100%;
  left: 0;
  transform: translateY(5px);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 0.85em;
  white-space: nowrap;
  box-shadow: 0 2px 5px rgba(0,0,0,0.25);
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.usage-info {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ddd;
}

.usage-info span:first-child {
  font-weight: 500;
}

/* 如果需要，可以为 SessionID 单独添加样式 */
/*
.session-id-info {
}
*/

.timestamp {
  display: inline-block;
}

/* 新增：耗时信息样式 */
.loading-duration-display {
  margin-left: 8px; /* 和时间戳分开一点 */
  color: #999; /* 比时间戳颜色稍浅 */
}

.message.ai .message-footer {
  color: #777;
}

.message.ai .session-toggle-icon {
  color: #999;
}

.message-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: calc(100% - 40px - 16px);
}

.message-content-wrapper .message.ai {
  margin-left: 0;
}

.message-content-wrapper.user {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 8px;
  max-width: calc(100% - 40px - 16px);
}

.message-row.user .message.user {
  background: linear-gradient(135deg, #007bff 60%, #5bc0eb 100%);
  color: #fff;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 18px;
  margin-right: 0;
  margin-left: 0;
}

.message-row.user .message-footer {
  color: #888;
  font-size: 0.75em;
  margin-top: 4px;
  margin-right: 0;
  padding: 0 2px;
  width: auto;
  display: flex;
  justify-content: flex-end;
}

.message-row.user .session-info-container {
  display: none;
}

/* Styles for disabled input and buttons */
.input-area input:disabled {
  background-color: #e9ecef; /* Light gray background */
  cursor: not-allowed;       /* "Not allowed" cursor */
  opacity: 0.7;              /* Slightly transparent */
}

.input-area button:disabled {
  background: #adb5bd;    /* Gray background */
  cursor: not-allowed;      /* "Not allowed" cursor */
  opacity: 0.6;             /* More transparent */
  box-shadow: none;         /* Remove shadow */
}

/* Ensure disabled send button's SVG color is appropriate */
.input-area button.send-btn:disabled svg {
  color: #f8f9fa; /* Lighter color for the icon */
}

/* Ensure disabled clear button's SVG color is appropriate */
.input-area button.clear-btn:disabled svg {
  color: #f8f9fa; /* Lighter color for the icon */
}

/* Loading dots animation */
.loading-dots span {
  animation: blink 1.4s infinite both;
  font-weight: bold;
  font-size: 1.2em; /* 让点更明显一点 */
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0% { opacity: 0.2; }
  20% { opacity: 1; }
  100% { opacity: 0.2; }
}

/* 新增：下拉选择框样式 */
.biz-params-selectors {
  display: flex;
  gap: 15px; /* 下拉框之间的间距 */
  padding: 10px 15px 5px 15px; /* 内边距 (上 右 下 左) */
  border-top: 1px solid #e0e0e0;
  background-color: #f9f9f9; /* 背景色 */
}

.biz-params-selectors label {
  display: flex;
  align-items: center;
  gap: 5px; /* 标签文字和下拉框的间距 */
  font-size: 0.9em;
  color: #555;
}

.biz-params-selectors select {
  padding: 6px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  font-size: 0.9em;
  cursor: pointer;
  min-width: 100px; /* 保证一定宽度 */
}

.biz-params-selectors select:disabled {
  background-color: #eee;
  cursor: not-allowed;
}

/* 候选答案样式 */
.candidate-answers {
  margin: 8px 0;
  padding: 10px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}
.candidate-title {
  font-size: 0.9em;
  font-weight: 600;
  margin-bottom: 6px;
  color: #333;
}
.candidate-answers ul {
  list-style: decimal inside;
  padding: 0;
  margin: 0;
}
.candidate-answers li {
  padding: 4px 0;
  color: #555;
  font-size: 0.85em;
}

/* 候选答案单选框样式 */
.candidate-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}
.candidate-item input[type="radio"] {
  cursor: pointer;
  accent-color: #007bff;
}

/* 添加：候选答案标签宽度 */
.candidate-label {
  display: inline-block;
  min-width: 4em;
  margin-right: 5px;
}

/* 新增：候选答案表格样式 */
.candidate-table {
  width: 100%;
  border-collapse: collapse; /* 合并边框 */
  margin-top: 5px;
}

.candidate-table th, .candidate-table td {
  border: 1px solid #eee; /* 添加细边框 */
  padding: 6px 8px; /* 单元格内边距 */
  text-align: left;
}

.candidate-table th {
  background-color: #f8f9fa; /* 表头背景色 */
  font-weight: bold;
  font-size: 0.9em; /* 字体稍微小一点 */
}

/* 列宽设置 */
.candidate-table .col-select {
  width: 5%;
}
.candidate-table .col-score {
  width: 5%;
}
.candidate-table .col-answer {
  width: 50%;
}
.candidate-table .col-reason {
  width: 40%;
}

/* 选中行的样式 */
.candidate-table tr.selected td {
  background-color: #e6f7ff; /* 浅蓝色背景 */
}

.candidate-table td input[type="radio"] {
  vertical-align: middle; /* 垂直居中单选按钮 */
}

.candidate-table .candidate-score {
  font-family: monospace; /* 等宽字体 */
}

.candidate-table .candidate-reason {
  color: #555; /* 原因文字颜色稍深 */
  font-size: 0.9em;
  white-space: pre-wrap; /* 新增：保留换行符和空格 */
}

/* 修改加载指示器的样式 */
.message.loading-indicator {
  background-color: #e9e9eb;
  color: #333;
  align-self: flex-start;
  /* margin-left: 10px; */ /* 移除，让wrapper控制 */
  padding: 10px 15px;
  border-radius: 18px;
  display: flex; /* 改为 flex 布局 */
  align-items: baseline; /* 基线对齐 */
}

/* 新增：计时器样式 */
.loading-timer {
  margin-left: 8px; /* 与点分开一点 */
  font-size: 0.9em; /* 比消息文本小一点 */
  color: #666; /* 灰色 */
}

/* 新增：思考过程样式 */
.thinking-process {
  margin: 8px 0;
  padding: 10px;
  background: #f7f7f7;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

/* 新增：思考过程标题样式 */
.thinking-title {
  font-size: 0.9em;
  font-weight: 600;
  margin-bottom: 6px;
  color: #333;
}

/* 新增：思考过程内容样式 */
.thinking-content {
  font-size: 0.85em;
  color: #444;
  white-space: pre-wrap;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #eee;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  line-height: 1.4;
}

/* 新增：玩家问题重写样式 */
.rewritten-query-process {
  margin: 8px 0;
  padding: 10px;
  background: #f7f7f7;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.rewritten-query-title {
  font-size: 0.9em;
  font-weight: 600;
  margin-bottom: 6px;
  color: #333;
}

.rewritten-query-content {
  font-size: 0.85em;
  color: #444;
  white-space: pre-wrap;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #eee;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  line-height: 1.4;
}

/* 新增：请求ID显示样式 */
.request-id-display {
  font-size: 0.75em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  color: #666;
  margin-bottom: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  word-break: break-all;
  line-height: 1.2;
}

/* 用户请求ID样式 - 右对齐 */
.user-request-id {
  text-align: right;
  background-color: rgba(0, 123, 255, 0.1);
  border-color: rgba(0, 123, 255, 0.2);
  color: #0056b3;
}

/* AI响应ID样式 - 左对齐 */
.ai-request-id {
  text-align: left;
  background-color: rgba(108, 117, 125, 0.1);
  border-color: rgba(108, 117, 125, 0.2);
  color: #495057;
}

/* AI响应ID缺失样式 */
.ai-request-id.missing {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  font-style: italic;
}
