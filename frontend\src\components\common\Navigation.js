import React from 'react';
import { NavLink } from 'react-router-dom';
import './Navigation.css';

function Navigation() {
  return (
    <nav className="navigation">
      <div className="nav-container">
        <div className="nav-title">AI智能客服系统</div>
        <div className="nav-links">
          <NavLink 
            to="/chat" 
            className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
          >
            💬 单问对话
          </NavLink>
          <NavLink 
            to="/batch-test" 
            className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
          >
            📋 批量测试
          </NavLink>
          <NavLink 
            to="/recorder" 
            className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}
          >
            📝 FAQ录入
          </NavLink>
        </div>
      </div>
    </nav>
  );
}

export default Navigation; 