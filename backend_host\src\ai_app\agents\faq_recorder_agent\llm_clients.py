"""
FAQ录入代理的LLM客户端封装

提供专门用于FAQ录入任务的LLM客户端，包括问答分析和分类推荐。
"""

import json
import jinja2
from typing import List, Dict, Any, Tuple, Optional

from ai_app.models.chat import ChatModelUsage
from ai_app.models.faq_recorder import FAQRecorderQuestionAnswer, CategoryRecommendation
from ai_app.shared.exceptions import LLMAPIError, LLMResponseError, ExamineQAPairError
from ai_app.agents.shared.llm_impl.base_llm_impl import BaseLLMImpl, DEFAULT_TIMEOUT
from ai_app.shared.request_tracing import get_traced_logger
from ai_app.config import config

logger = get_traced_logger(__name__)


class FAQExamineClient:
    """
    FAQ问答分析客户端
    
    使用LLM分析问答对的内容特征，提取关键信息用于后续处理。
    """
    
    def __init__(self, llm_client: BaseLLMImpl):
        """
        初始化分析客户端
        
        Args:
            llm_client: LLM实现实例
        """
        self.llm_client = llm_client
        
        # 加载系统提示词
        try:
            with open(config.faq_examine_system_prompt_path, 'r', encoding='utf-8') as f:
                self.system_prompt = f.read()
        except Exception as e:
            logger.error(f"Failed to load examination system prompt: {e}")
            raise ExamineQAPairError(f"Failed to load system prompt: {e}") from e
        
        # 加载用户提示词模板
        try:
            with open(config.faq_examine_user_prompt_path, 'r', encoding='utf-8') as f:

                user_prompt_template = f.read()
            self.user_prompt_template = jinja2.Template(user_prompt_template)
        except jinja2.exceptions.TemplateSyntaxError as e:
            logger.error(f"Invalid Jinja2 template syntax in examination user prompt: {e}")
            raise ExamineQAPairError(f"Invalid template syntax: {e}") from e
        except Exception as e:
            logger.error(f"Failed to load examination user prompt template: {e}")
            raise ExamineQAPairError(f"Failed to load user prompt template: {e}") from e
        
        logger.debug("FAQExamineClient initialized with template prompts")
    
    async def examine_qa_pair(
        self,
        qa_pair: FAQRecorderQuestionAnswer,
        timeout: float = DEFAULT_TIMEOUT
    ) -> Tuple[Dict[str, Any], ChatModelUsage]:
        """
        分析问答对内容
        
        Args:
            qa_pair: 问答对数据
            timeout: 请求超时时间
            
        Returns:
            (分析结果, 使用统计)
            
        Raises:
            ExamineQAPairError: 分析失败
        """
        try:
            logger.info(f"Starting QA pair examination for question: {qa_pair.question[:50]}...")
            
            # 构建用户提示词
            try:
                user_prompt = self.user_prompt_template.render(
                    question=qa_pair.question,
                    answer=qa_pair.answer,
                    question_example=qa_pair.question_example
                )
            except jinja2.exceptions.UndefinedError as e:
                logger.error(f"Jinja2 rendering error: Undefined variable {e}")
                raise ExamineQAPairError(f"Failed to render examination prompt: Undefined variable {e}") from e
            except Exception as e:
                logger.error(f"Error rendering examination prompt: {e}")
                raise ExamineQAPairError(f"Failed to render examination prompt: {e}") from e
            
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用LLM
            content, usage, _ = await self.llm_client.chat_completion(
                messages=messages,
                timeout=timeout,
                temperature=0.3,  # 较低温度确保分析一致性
            )
            
            # 解析分析结果
            examine_result = self._parse_examine_result(content)
            
            logger.info("QA pair examination completed successfully")
            return examine_result, usage
            
        except (LLMAPIError, LLMResponseError):
            raise
        except Exception as e:
            logger.error(f"Error during QA pair examination: {e}")
            raise ExamineQAPairError(f"Failed to examine QA pair: {e}") from e
    
    
    def _parse_examine_result(self, content: str) -> Dict[str, Any]:
        """
        解析LLM分析结果
        
        Args:
            content: LLM返回的内容
            
        Returns:
            解析后的分析结果
            
        Raises:
            ExamineQAPairError: 解析失败
        """
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            
            # 尝试从文本中提取JSON
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            # 如果无法解析为JSON，创建基础结果
            logger.warning("Could not parse examination result as JSON, creating basic result")
            return {
                "topic_domain": "未识别",
                "question_type": "一般询问",
                "keywords": [],
                "applicable_scenarios": [],
                "category_suggestions": [],
                "confidence": 0.5,
                "reasoning": f"LLM返回内容: {content[:200]}..."
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse examination result: {e}")
            raise ExamineQAPairError(f"Invalid examination result format: {e}") from e


class FAQCategoryRecommendClient:
    """
    分类推荐客户端
    
    基于现有分类结构和问答内容，使用LLM推荐合适的分类路径。
    """
    
    def __init__(self, llm_client: BaseLLMImpl):
        """
        初始化推荐客户端
        
        Args:
            llm_client: LLM实现实例
        """
        self.llm_client = llm_client
        
        # 加载系统提示词
        try:
            with open(config.faq_category_recommend_system_prompt_path, 'r', encoding='utf-8') as f:
                self.system_prompt = f.read()
        except Exception as e:
            logger.error(f"Failed to load category recommend system prompt: {e}")
            raise ExamineQAPairError(f"Failed to load system prompt: {e}") from e
        
        # 加载用户提示词模板
        try:
            with open(config.faq_category_recommend_user_prompt_path, 'r', encoding='utf-8') as f:
                user_prompt_template = f.read()
            self.user_prompt_template = jinja2.Template(user_prompt_template)
        except jinja2.exceptions.TemplateSyntaxError as e:
            logger.error(f"Invalid Jinja2 template syntax in category recommend user prompt: {e}")
            raise ExamineQAPairError(f"Invalid template syntax: {e}") from e
        except Exception as e:
            logger.error(f"Failed to load category recommend user prompt template: {e}")
            raise ExamineQAPairError(f"Failed to load user prompt template: {e}") from e
        
        logger.debug("FAQCategoryRecommendClient initialized with template prompts")
    
    async def recommend_categories(
        self,
        qa_pair: FAQRecorderQuestionAnswer,
        existing_categories: str,
        max_recommendations: int = 5,
        timeout: float = DEFAULT_TIMEOUT
    ) -> Tuple[List[CategoryRecommendation], ChatModelUsage]:
        """
        推荐分类路径
        
        Args:
            qa_pair: 问答对数据
            existing_categories: 现有分类结构（tree格式）
            max_recommendations: 最大推荐数量
            timeout: 请求超时时间
            
        Returns:
            (推荐结果列表, 使用统计)
            
        Raises:
            ExamineQAPairError: 推荐失败
        """
        try:
            logger.info(f"Starting category recommendation for question: {qa_pair.question[:50]}...")
            
            # 构建用户提示词
            try:
                user_prompt = self.user_prompt_template.render(
                    existing_categories=existing_categories,
                    question=qa_pair.question,
                    answer=qa_pair.answer,
                    question_example=qa_pair.question_example,
                    max_recommendations=max_recommendations
                )
            except jinja2.exceptions.UndefinedError as e:
                logger.error(f"Jinja2 rendering error: Undefined variable {e}")
                raise ExamineQAPairError(f"Failed to render recommendation prompt: Undefined variable {e}") from e
            except Exception as e:
                logger.error(f"Error rendering recommendation prompt: {e}")
                raise ExamineQAPairError(f"Failed to render recommendation prompt: {e}") from e
            
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用LLM
            content, usage, _ = await self.llm_client.chat_completion(
                messages=messages,
                timeout=timeout,
                temperature=0.2,  # 低温度确保推荐一致性
            )
            
            # 解析推荐结果
            recommendations = self._parse_recommendation_result(content)
            
            logger.info(f"Category recommendation completed, generated {len(recommendations)} recommendations")
            return recommendations, usage
            
        except (LLMAPIError, LLMResponseError):
            raise
        except Exception as e:
            logger.error(f"Error during category recommendation: {e}")
            raise ExamineQAPairError(f"Failed to recommend categories: {e}") from e
    
    
    def _parse_recommendation_result(self, content: str) -> List[CategoryRecommendation]:
        """
        解析LLM推荐结果
        
        Args:
            content: LLM返回的内容
            
        Returns:
            推荐结果列表
            
        Raises:
            ExamineQAPairError: 解析失败
        """
        try:
            # 尝试解析JSON
            result_data = None
            
            if content.strip().startswith('{'):
                result_data = json.loads(content)
            else:
                # 从文本中提取JSON
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    result_data = json.loads(json_match.group())
            
            if not result_data or 'recommendations' not in result_data:
                logger.warning("No valid recommendations found in LLM response")
                return []
            
            # 转换为CategoryRecommendation对象
            recommendations = []
            for rec_data in result_data['recommendations']:
                try:
                    recommendation = CategoryRecommendation(
                        category_path=rec_data.get('category_path', []),
                        confidence=min(max(rec_data.get('confidence', 0.5), 0.0), 1.0),
                        reason=rec_data.get('reason', ''),
                        is_new_category=rec_data.get('is_new_category', False),
                        similar_existing=rec_data.get('similar_existing', [])
                    )
                    recommendations.append(recommendation)
                except Exception as e:
                    logger.warning(f"Failed to parse recommendation item: {e}")
                    continue
            
            return recommendations
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse recommendation result: {e}")
            # 返回空列表而不是抛出异常，让调用方有机会使用其他推荐方式
            return []
        except Exception as e:
            logger.error(f"Unexpected error parsing recommendations: {e}")
            return []


class FAQContentEnhancementClient:
    """
    FAQ内容增强客户端
    
    使用LLM优化问答内容的质量和表达。
    """
    
    def __init__(self, llm_client: BaseLLMImpl):
        """
        初始化内容增强客户端
        
        Args:
            llm_client: LLM实现实例
        """
        self.llm_client = llm_client
        
        # 加载系统提示词
        try:
            with open(config.faq_content_enhance_system_prompt_path, 'r', encoding='utf-8') as f:
                self.system_prompt = f.read()
        except Exception as e:
            logger.error(f"Failed to load content enhance system prompt: {e}")
            raise ExamineQAPairError(f"Failed to load system prompt: {e}") from e
        
        # 加载用户提示词模板
        try:
            with open(config.faq_content_enhance_user_prompt_path, 'r', encoding='utf-8') as f:
                user_prompt_template = f.read()
            self.user_prompt_template = jinja2.Template(user_prompt_template)
        except jinja2.exceptions.TemplateSyntaxError as e:
            logger.error(f"Invalid Jinja2 template syntax in content enhance user prompt: {e}")
            raise ExamineQAPairError(f"Invalid template syntax: {e}") from e
        except Exception as e:
            logger.error(f"Failed to load content enhance user prompt template: {e}")
            raise ExamineQAPairError(f"Failed to load user prompt template: {e}") from e
        
        # 预定义增强类型指令
        self.enhancement_instructions = {
            "general": "请优化问答内容，使其更清晰、准确、易懂",
            "formal": "请将问答内容调整为更正式、专业的表达方式",
            "detailed": "请扩展问答内容，提供更详细的说明和步骤",
            "concise": "请精简问答内容，保留核心信息，使其更简洁"
        }
        
        logger.debug("FAQContentEnhancementClient initialized with template prompts")
    
    async def enhance_qa_content(
        self,
        qa_pair: FAQRecorderQuestionAnswer,
        enhancement_type: str = "general",
        timeout: float = DEFAULT_TIMEOUT
    ) -> Tuple[FAQRecorderQuestionAnswer, ChatModelUsage]:
        """
        增强问答内容
        
        Args:
            qa_pair: 原始问答对
            enhancement_type: 增强类型（general, formal, detailed等）
            timeout: 请求超时时间
            
        Returns:
            (增强后的问答对, 使用统计)
            
        Raises:
            ExamineQAPairError: 增强失败
        """
        try:
            logger.info(f"Starting content enhancement for: {qa_pair.question[:50]}...")
            
            # 获取增强指令
            instruction = self.enhancement_instructions.get(
                enhancement_type, 
                self.enhancement_instructions["general"]
            )
            
            # 构建用户提示词
            try:
                user_prompt = self.user_prompt_template.render(
                    enhancement_type=enhancement_type,
                    question=qa_pair.question,
                    answer=qa_pair.answer,
                    question_example=qa_pair.question_example,
                    instruction=instruction
                )
            except jinja2.exceptions.UndefinedError as e:
                logger.error(f"Jinja2 rendering error: Undefined variable {e}")
                raise ExamineQAPairError(f"Failed to render enhancement prompt: Undefined variable {e}") from e
            except Exception as e:
                logger.error(f"Error rendering enhancement prompt: {e}")
                raise ExamineQAPairError(f"Failed to render enhancement prompt: {e}") from e
            
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 调用LLM
            content, usage, _ = await self.llm_client.chat_completion(
                messages=messages,
                timeout=timeout,
                temperature=0.4,  # 适中温度保持创意性
            )
            
            # 解析增强结果
            enhanced_qa = self._parse_enhancement_result(content, qa_pair)
            
            logger.info("Content enhancement completed successfully")
            return enhanced_qa, usage
            
        except (LLMAPIError, LLMResponseError):
            raise
        except Exception as e:
            logger.error(f"Error during content enhancement: {e}")
            raise ExamineQAPairError(f"Failed to enhance content: {e}") from e
    
    
    def _parse_enhancement_result(
        self,
        content: str,
        original_qa: FAQRecorderQuestionAnswer
    ) -> FAQRecorderQuestionAnswer:
        """
        解析内容增强结果
        
        Args:
            content: LLM返回的内容
            original_qa: 原始问答对
            
        Returns:
            增强后的问答对
        """
        try:
            # 尝试解析JSON
            result_data = None
            
            if content.strip().startswith('{'):
                result_data = json.loads(content)
            else:
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    result_data = json.loads(json_match.group())
            
            if not result_data:
                logger.warning("Could not parse enhancement result, returning original")
                return original_qa
            
            # 创建增强后的问答对
            enhanced_qa = FAQRecorderQuestionAnswer(
                question=result_data.get('enhanced_question', original_qa.question),
                answer=result_data.get('enhanced_answer', original_qa.answer),
                question_example=result_data.get('enhanced_question_example', original_qa.question_example)
            )
            
            return enhanced_qa
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse enhancement result: {e}")
            return original_qa
        except Exception as e:
            logger.error(f"Unexpected error parsing enhancement: {e}")
            return original_qa