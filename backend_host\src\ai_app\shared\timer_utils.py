"""计时工具类，用于优雅地记录API调用耗时。"""

import time
from typing import Optional
from contextlib import contextmanager


class Timer:
    """计时器类，用于记录代码执行耗时。"""
    
    def __init__(self):
        """初始化计时器。"""
        self._start_time: Optional[float] = None
        self._end_time: Optional[float] = None
    
    def start(self) -> 'Timer':
        """开始计时。
        
        Returns:
            Timer: 返回自身，支持链式调用。
        """
        self._start_time = time.time()
        self._end_time = None
        return self
    
    def stop(self) -> 'Timer':
        """停止计时。
        
        Returns:
            Timer: 返回自身，支持链式调用。
        """
        if self._start_time is None:
            raise ValueError("Timer has not been started")
        self._end_time = time.time()
        return self
    
    def get_duration_ms(self) -> int:
        """获取耗时（毫秒）。
        
        Returns:
            int: 耗时毫秒数。
            
        Raises:
            ValueError: 如果计时器未启动或未停止。
        """
        if self._start_time is None:
            raise ValueError("Timer has not been started")
        
        end_time = self._end_time if self._end_time is not None else time.time()
        duration_seconds = end_time - self._start_time
        return int(duration_seconds * 1000)
    
    def get_duration_seconds(self) -> float:
        """获取耗时（秒）。
        
        Returns:
            float: 耗时秒数。
            
        Raises:
            ValueError: 如果计时器未启动。
        """
        if self._start_time is None:
            raise ValueError("Timer has not been started")
        
        end_time = self._end_time if self._end_time is not None else time.time()
        return end_time - self._start_time
    
    @property
    def is_running(self) -> bool:
        """检查计时器是否正在运行。
        
        Returns:
            bool: 如果计时器已启动但未停止，返回True。
        """
        return self._start_time is not None and self._end_time is None
    
    @property
    def is_stopped(self) -> bool:
        """检查计时器是否已停止。
        
        Returns:
            bool: 如果计时器已启动且已停止，返回True。
        """
        return self._start_time is not None and self._end_time is not None


@contextmanager
def timer_context():
    """计时器上下文管理器，自动开始和停止计时。
    
    Usage:
        with timer_context() as timer:
            # 执行需要计时的代码
            pass
        duration_ms = timer.get_duration_ms()
    
    Yields:
        Timer: 已启动的计时器实例。
    """
    timer = Timer().start()
    try:
        yield timer
    finally:
        timer.stop()


def create_timer() -> Timer:
    """创建并启动一个新的计时器。
    
    Returns:
        Timer: 已启动的计时器实例。
    """
    return Timer().start()