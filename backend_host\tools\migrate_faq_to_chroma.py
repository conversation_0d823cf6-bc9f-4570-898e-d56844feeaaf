"""
ChromaDB 数据迁移工具

功能:
- 从源FAQ Excel文件加载数据。
- 为每个FAQ条目生成向量嵌入 (embedding)。
- 将数据转换为ChromaDB格式。
- 使用 'upsert' 方法批量写入ChromaDB，支持增量更新。
"""
import os
import sys
import argparse
import logging
import asyncio
import requests
from typing import List, Dict, Any

import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
from chromadb import Documents, EmbeddingFunction, Embeddings

from src.ai_app.config import config, get_logging_config
from src.ai_app.services.faq_management.faq_repo_manager import FAQRepoManager
from src.ai_app.services.faq_management.data_parser import FAQDataParser

# 配置日志记录
logger = logging.getLogger(__name__)


class CustomEmbeddingFunction(EmbeddingFunction):
    """
    自定义的EmbeddingFunction，支持多种embedding提供者

    支持的提供者：
    - openai: OpenAI API兼容的embedding服务
    - ollama: Ollama本地embedding服务
    - volcano: 火山引擎embedding服务
    """

    def __init__(self, provider: str = "openai", **kwargs):
        """
        初始化自定义embedding函数

        Args:
            provider: embedding提供者 ("openai", "ollama", "volcano")
            **kwargs: 各提供者的特定参数
        """
        self.provider = provider.lower()
        self.kwargs = kwargs

        # 根据提供者设置默认参数
        if self.provider == "openai":
            self.api_base = kwargs.get("api_base", config.chroma_api_base)
            self.api_key = kwargs.get("api_key", config.chroma_api_key)
            self.model_name = kwargs.get("model_name", config.chroma_model)
        elif self.provider == "ollama":
            self.url = kwargs.get("url", "http://localhost:11434")
            self.model_name = kwargs.get("model_name", config.chroma_model)
        elif self.provider == "volcano":
            self.api_base = kwargs.get("api_base", config.volcano_knowledge_api_base)
            self.api_key = kwargs.get("api_key", config.volcano_knowledge_api_ak)
            self.model_name = kwargs.get("model_name", "bge-m3")
        else:
            raise ValueError(f"不支持的embedding提供者: {provider}")

        logger.info(f"初始化自定义EmbeddingFunction，提供者: {self.provider}, 模型: {self.model_name}")

    def __call__(self, input: Documents) -> Embeddings:
        """
        生成文档的embedding向量

        Args:
            input: 输入文档列表

        Returns:
            Embeddings: 生成的embedding向量列表
        """
        try:
            if self.provider == "openai":
                return self._openai_embed(input)
            elif self.provider == "ollama":
                return self._ollama_embed(input)
            elif self.provider == "volcano":
                return self._volcano_embed(input)
            else:
                raise ValueError(f"不支持的embedding提供者: {self.provider}")
        except Exception as e:
            logger.error(f"生成embedding时发生错误: {e}")
            raise

    def _openai_embed(self, texts: List[str]) -> List[List[float]]:
        """使用OpenAI API兼容的服务生成embedding"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "input": texts,
            "model": self.model_name
        }

        response = requests.post(
            f"{self.api_base}/embeddings",
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()

        result = response.json()
        embeddings = [item["embedding"] for item in result["data"]]
        logger.debug(f"OpenAI embedding生成成功，文档数量: {len(texts)}")
        return embeddings

    def _ollama_embed(self, texts: List[str]) -> List[List[float]]:
        """使用Ollama本地服务生成embedding"""
        embeddings = []

        for text in texts:
            data = {
                "model": self.model_name,
                "prompt": text
            }

            response = requests.post(
                f"{self.url}/api/embeddings",
                json=data,
                timeout=30
            )
            response.raise_for_status()

            result = response.json()
            embeddings.append(result["embedding"])

        logger.debug(f"Ollama embedding生成成功，文档数量: {len(texts)}")
        return embeddings

    def _volcano_embed(self, texts: List[str]) -> List[List[float]]:
        """使用火山引擎服务生成embedding"""
        # 这里可以根据火山引擎的API规范实现
        # 目前作为示例，使用OpenAI兼容的方式
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "input": texts,
            "model": self.model_name
        }

        response = requests.post(
            f"{self.api_base}/embeddings",
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()

        result = response.json()
        embeddings = [item["embedding"] for item in result["data"]]
        logger.debug(f"Volcano embedding生成成功，文档数量: {len(texts)}")
        return embeddings


# --- Core Migration Logic ---
async def migrate_data_to_chroma(
    batch_size: int,
    recreate: bool = False
):
    """
    执行从Excel到ChromaDB的数据迁移。
    """
    logger.info("--- 开始执行数据迁移到 ChromaDB ---")
    
    # 1. 初始化 FAQRepoManager 并加载数据
    logger.info(f"从 {config.faq_excel_path} 加载FAQ数据...")
    try:
        faq_manager = FAQRepoManager(excel_path=config.faq_excel_path)
        if not faq_manager.load_from_excel():
            logger.error("加载FAQ数据失败，请检查Excel文件和路径。")
            return
    except Exception as e:
        logger.error(f"初始化FAQ管理器时发生错误: {e}")
        return

    # 2. 初始化 ChromaDB 客户端并配置 Embedding Function
    logger.info(f"连接到 ChromaDB at {config.chroma_host}:{config.chroma_port}, embedding model: {config.chroma_model}...")
    try:
        chroma_client = await chromadb.AsyncHttpClient(host=config.chroma_host, port=config.chroma_port, settings=Settings(anonymized_telemetry=False))
        
        embed_fn = embedding_functions.OpenAIEmbeddingFunction(
            api_base=config.chroma_api_base,
            api_key=config.chroma_api_key,
            model_name=config.chroma_model
        )
        embed_fn = embedding_functions.OllamaEmbeddingFunction(model_name=config.chroma_model)
        
        if recreate:
            logger.warning(f"检测到 --recreate 参数，将尝试删除 Collection '{config.embedding_collection_name}'...")
            try:
                await chroma_client.delete_collection(name=config.embedding_collection_name)
                logger.info(f"Collection '{config.embedding_collection_name}' 已成功删除。")
            except Exception as e:
                # 如果 collection 不存在，ChromaDB 可能会报错，这是正常现象
                logger.warning(f"删除 Collection '{config.embedding_collection_name}' 时出现问题 (可能它不存在): {e}")

        collection = await chroma_client.get_or_create_collection(
            name=config.embedding_collection_name,
            embedding_function=embed_fn,
            configuration={
                "hnsw": {
                    "space": "cosine",
                }
            },
        )
        logger.info(f"成功连接到 Collection '{config.embedding_collection_name}'。")
    except Exception as e:
        logger.error(f"连接 ChromaDB 失败: {e}")
        return

    # 3. 遍历所有 channels 并处理数据
    channels = faq_manager.faq_data.keys()
    logger.info(f"发现 {len(channels)} 个 channels: {list(channels)}")

    total_upserted_count = 0
    for channel in channels:
        logger.info(f"--- 正在处理 Channel: {channel} ---")
        faq_parser = faq_manager.create_parser(channel)

        # 准备批量处理的数据
        batch_ids: List[str] = []
        batch_documents: List[str] = []
        batch_metadatas: List[Dict[str, Any]] = []

        all_answers = []
        faq_parser.collect_answers_recursively(all_answers)

        for i, item in enumerate(all_answers):
            # 按照 CHROMA_INTEINTEGRATION.md 的设计构建数据
            doc_id = f"{channel}_{item['key_path'].replace('.', '_')}"
            description_path_to_string = FAQDataParser.description_path_to_string(item['desc_path'])
            metadata = {
                "channel": channel,
                "category_key_path": item['key_path'],
                "category_desc": description_path_to_string
            }
            document = f"""
## 问题类别
`{metadata['category_desc']}`
## 问题示例
{item['question_example']}
## 参考答案
{item['answer']}
            """
            
            batch_ids.append(doc_id)
            batch_documents.append(document)
            batch_metadatas.append(metadata)

            # 当达到 batch_size 时，处理批次
            if len(batch_ids) >= batch_size or i == len(all_answers) - 1:
                try:
                    await collection.upsert(
                        ids=batch_ids,
                        documents=batch_documents,
                        metadatas=batch_metadatas
                    )
                    logger.info(f"成功写入 {len(batch_ids)} 条记录到 ChromaDB。")
                    total_upserted_count += len(batch_ids)
                    # 清空批次
                    batch_ids, batch_documents, batch_metadatas = [], [], []
                except Exception as e:
                    logger.error(f"写入 ChromaDB 时发生错误: {e}")
                    raise

    logger.info(f"--- 数据迁移完成 ---")
    logger.info(f"总共处理并写入了 {total_upserted_count} 条记录。")

# --- CLI Interface ---
async def main():
    parser = argparse.ArgumentParser(
        description="将FAQ Excel数据迁移到ChromaDB的工具。该工具会从环境变量或 .env 文件中读取Chroma和Embedding服务的配置。"
    )
    
    parser.add_argument("--batch-size", type=int, default=5, help="批量处理数据的大小。")
    parser.add_argument(
        "--recreate",
        action="store_true",
        help="如果指定，将在迁移数据前先删除已存在的同名Collection。"
    )
    parser.add_argument("-v", "--verbose", action="store_true", help="启用DEBUG级别的日志记录。")

    args = parser.parse_args()

    # 配置日志
    logging.config.dictConfig(get_logging_config(cli_verbose_flag=args.verbose))

    await migrate_data_to_chroma(
        batch_size=args.batch_size,
        recreate=args.recreate
    )

# 运行主程序
if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("操作被用户中断。")
        sys.exit(0)