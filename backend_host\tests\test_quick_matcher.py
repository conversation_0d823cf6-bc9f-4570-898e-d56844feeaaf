#!/usr/bin/env python3
"""
测试QuickMatcher类的功能

测试基于fuzzywuzzy的文本相似度匹配和去重功能。
"""

import unittest
import sys
import os
import logging
from typing import List, Dict, Any
from ai_app.config import config, get_logging_config

from ai_app.shared.quick_matcher import QuickMatcher

class TestQuickMatcher(unittest.TestCase):
    """QuickMatcher类的单元测试"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.matcher = QuickMatcher(similarity_threshold=0.8)
        
        # 测试数据
        self.test_texts = [
            "您好，请问如何修改密码？",
            "你好，怎么改密码呢？",
            "密码修改的方法是什么？",
            "如何充值游戏币？",
            "游戏币充值流程",
            "账号被封了怎么办？",
            "我的账号被封禁了"
        ]
        
        self.test_items = [
            {"content": "您好，请问如何修改密码？", "score": 0.95, "id": 1},
            {"content": "你好，怎么改密码呢？", "score": 0.85, "id": 2},
            {"content": "密码修改的方法是什么？", "score": 0.75, "id": 3},
            {"content": "如何充值游戏币？", "score": 0.90, "id": 4},
            {"content": "游戏币充值流程", "score": 0.80, "id": 5},
            {"content": "账号被封了怎么办？", "score": 0.70, "id": 6},
            {"content": "我的账号被封禁了", "score": 0.65, "id": 7}
        ]
    
    def test_init(self):
        """测试初始化"""
        # 正常初始化
        matcher = QuickMatcher(0.7)
        self.assertEqual(matcher.similarity_threshold, 0.7)
        
        # 边界值测试
        matcher_min = QuickMatcher(0.0)
        self.assertEqual(matcher_min.similarity_threshold, 0.0)
        
        matcher_max = QuickMatcher(1.0)
        self.assertEqual(matcher_max.similarity_threshold, 1.0)
        
        # 异常值测试
        with self.assertRaises(ValueError):
            QuickMatcher(-0.1)
        
        with self.assertRaises(ValueError):
            QuickMatcher(1.1)
    
    def test_calculate_similarity(self):
        """测试相似度计算"""
        # 完全相同的文本
        similarity = self.matcher.calculate_similarity("hello", "hello")
        self.assertEqual(similarity, 1.0)
        
        # 完全不同的文本
        similarity = self.matcher.calculate_similarity("hello", "world")
        self.assertLess(similarity, 0.5)
        
        # 测试相似文本
        similarity = self.matcher.calculate_similarity("您好，请问如何修改密码？", "你好，怎么改密码呢？")
        self.assertGreaterEqual(similarity, 0.5)
        
        # 空字符串测试
        similarity = self.matcher.calculate_similarity("", "")
        self.assertEqual(similarity, 1.0)
        
        similarity = self.matcher.calculate_similarity("hello", "")
        self.assertEqual(similarity, 0.0)
        
        # 非字符串输入测试
        similarity = self.matcher.calculate_similarity(None, "hello")
        self.assertEqual(similarity, 0.0)
    
    def test_calculate_similarity_methods(self):
        """测试不同的相似度计算方法"""
        text1 = "您好，请问如何修改密码？"
        text2 = "你好，怎么改密码呢？"
        
        methods = ["ratio", "partial_ratio", "token_sort_ratio", "token_set_ratio"]
        
        for method in methods:
            similarity = self.matcher.calculate_similarity(text1, text2, method)
            self.assertIsInstance(similarity, float)
            self.assertGreaterEqual(similarity, 0.0)
            self.assertLessEqual(similarity, 1.0)
        
        # 无效方法测试
        with self.assertRaises(ValueError):
            self.matcher.calculate_similarity(text1, text2, "invalid_method")
    
    def test_is_similar(self):
        """测试相似性判断"""
        # 相似的文本
        self.assertTrue(self.matcher.is_similar("您好，请问如何修改密码？", "你好，怎么改密码呢？", threshold=0.5))
        
        # 不相似的文本
        self.assertFalse(self.matcher.is_similar("如何修改密码？", "如何充值游戏币？", threshold=0.8))
        
        # 使用默认阈值
        matcher_low = QuickMatcher(0.3)
        self.assertTrue(matcher_low.is_similar("hello", "helo"))
    
    def test_find_most_similar(self):
        """测试查找最相似的文本"""
        query = "怎么修改密码"
        choices = self.test_texts
        
        results = self.matcher.find_most_similar(query, choices, limit=3)
        
        # 检查结果格式
        self.assertIsInstance(results, list)
        self.assertLessEqual(len(results), 3)
        
        for text, score in results:
            self.assertIsInstance(text, str)
            self.assertIsInstance(score, float)
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 1.0)
        
        # 检查结果是否按相似度降序排列
        scores = [score for _, score in results]
        self.assertEqual(scores, sorted(scores, reverse=True))
        
        # 空输入测试
        self.assertEqual(self.matcher.find_most_similar("", choices), [])
        self.assertEqual(self.matcher.find_most_similar(query, []), [])
    
    def test_deduplicate_by_content(self):
        """测试基于内容的去重"""
        # 测试去重功能
        deduplicated = self.matcher.deduplicate_by_content(
            self.test_items, 
            content_key="content", 
            score_key="score",
            threshold=0.7
        )
        
        # 检查去重结果
        self.assertIsInstance(deduplicated, list)
        self.assertLessEqual(len(deduplicated), len(self.test_items))
        
        # 检查是否保留了高分项目
        original_scores = [item["score"] for item in self.test_items]
        deduplicated_scores = [item["score"] for item in deduplicated]
        
        # 去重后的最高分应该等于原始数据的最高分
        self.assertEqual(max(deduplicated_scores), max(original_scores))
        
        # 空输入测试
        self.assertEqual(self.matcher.deduplicate_by_content([]), [])
    
    def test_deduplicate_texts(self):
        """测试文本去重"""
        # 测试去重功能
        deduplicated = self.matcher.deduplicate_texts(self.test_texts, threshold=0.7)
        
        # 检查去重结果
        self.assertIsInstance(deduplicated, list)
        self.assertLessEqual(len(deduplicated), len(self.test_texts))
        
        # 检查去重后的文本都是唯一的
        for i, text1 in enumerate(deduplicated):
            for j, text2 in enumerate(deduplicated):
                if i != j:
                    similarity = self.matcher.calculate_similarity(text1, text2)
                    self.assertLess(similarity, 0.7, f"Found similar texts after deduplication: '{text1}' vs '{text2}' (similarity: {similarity})")
        
        # 空输入测试
        self.assertEqual(self.matcher.deduplicate_texts([]), [])
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试包含非字符串内容的项目
        mixed_items = [
            {"content": "正常文本", "score": 0.9},
            {"content": None, "score": 0.8},
            {"content": 123, "score": 0.7},
            {"content": "另一个正常文本", "score": 0.6}
        ]
        
        deduplicated = self.matcher.deduplicate_by_content(mixed_items)
        
        # 应该只保留字符串内容的项目
        for item in deduplicated:
            self.assertIsInstance(item["content"], str)
    
    def test_performance_with_large_dataset(self):
        """测试大数据集的性能"""
        # 创建大量测试数据
        large_dataset = []
        base_texts = [
            "如何修改密码",
            "怎么充值",
            "账号被封",
            "游戏卡顿",
            "无法登录"
        ]
        
        for i in range(100):
            for base_text in base_texts:
                large_dataset.append({
                    "content": f"{base_text}_{i}",
                    "score": 0.5 + (i % 50) / 100.0,
                    "id": i * len(base_texts) + base_texts.index(base_text)
                })
        
        # 测试去重性能
        import time
        start_time = time.time()
        deduplicated = self.matcher.deduplicate_by_content(large_dataset, threshold=0.9)
        end_time = time.time()
        
        logging.info(f"Large dataset deduplication: {len(large_dataset)} -> {len(deduplicated)} items in {end_time - start_time:.3f} seconds")
        
        # 验证结果
        self.assertLessEqual(len(deduplicated), len(large_dataset))
        self.assertGreater(len(deduplicated), 0)


def run_interactive_test():
    """运行交互式测试"""
    print("=== QuickMatcher 交互式测试 ===")
    
    matcher = QuickMatcher(similarity_threshold=0.8)
    
    # 测试基本相似度计算
    print("\n1. 基本相似度计算测试:")
    test_pairs = [
        ("您好，请问如何修改密码？", "你好，怎么改密码呢？"),
        ("如何充值游戏币？", "游戏币充值流程"),
        ("账号被封了怎么办？", "我的账号被封禁了"),
        ("修改密码", "充值游戏币")
    ]
    
    for text1, text2 in test_pairs:
        similarity = matcher.calculate_similarity(text1, text2)
        is_similar = matcher.is_similar(text1, text2)
        print(f"  '{text1}' vs '{text2}'")
        print(f"    相似度: {similarity:.3f}, 是否相似: {is_similar}")
    
    # 测试查找最相似文本
    print("\n2. 查找最相似文本测试:")
    query = "怎么修改密码"
    choices = [
        "您好，请问如何修改密码？",
        "你好，怎么改密码呢？",
        "密码修改的方法是什么？",
        "如何充值游戏币？",
        "账号被封了怎么办？"
    ]
    
    results = matcher.find_most_similar(query, choices, limit=3)
    print(f"  查询: '{query}'")
    print("  最相似的文本:")
    for i, (text, score) in enumerate(results, 1):
        print(f"    {i}. '{text}' (相似度: {score:.3f})")
    
    # 测试去重功能
    print("\n3. 去重功能测试:")
    test_items = [
        {"content": "您好，请问如何修改密码？", "score": 0.95},
        {"content": "你好，怎么改密码呢？", "score": 0.85},
        {"content": "密码修改的方法是什么？", "score": 0.75},
        {"content": "如何充值游戏币？", "score": 0.90},
        {"content": "游戏币充值流程", "score": 0.80}
    ]
    
    print("  原始数据:")
    for i, item in enumerate(test_items, 1):
        print(f"    {i}. '{item['content']}' (分数: {item['score']})")
    
    deduplicated = matcher.deduplicate_by_content(test_items, threshold=0.7)
    print(f"\n  去重后 (阈值: 0.7):")
    for i, item in enumerate(deduplicated, 1):
        print(f"    {i}. '{item['content']}' (分数: {item['score']})")
    
    print(f"\n  去重统计: {len(test_items)} -> {len(deduplicated)} 项")


def run_custom_similarity_test(text1: str, text2: str, method: str = None):
    """
    运行自定义文本相似度计算测试
    
    Args:
        text1: 第一个文本
        text2: 第二个文本
        method: 相似度计算方法，如果为None则对比所有方法
    """
    def print_explanation(similarity: float):
        """打印相似度解释"""
        print("\n=== 相似度解释 ===")
        if similarity >= 0.9:
            print("🟢 非常相似 - 两个文本几乎相同")
        elif similarity >= 0.7:
            print("🟡 比较相似 - 两个文本有很多共同点")
        elif similarity >= 0.5:
            print("🟠 中等相似 - 两个文本有一些共同点")
        elif similarity >= 0.3:
            print("🔴 较少相似 - 两个文本有少量共同点")
        else:
            print("⚫ 不相似 - 两个文本基本没有共同点")

    print("=== 自定义文本相似度计算 ===")
    
    matcher = QuickMatcher(similarity_threshold=0.8)
    
    print(f"\n文本1: '{text1}'")
    print(f"文本2: '{text2}'")
    
    if method:
        # 指定方法模式：只使用指定的方法
        print(f"计算方法: {method}")
        
        # 计算相似度
        similarity = matcher.calculate_similarity(text1, text2, method)
        is_similar = matcher.is_similar(text1, text2, method)
        
        print(f"\n相似度分数: {similarity:.4f} (0.0-1.0)")
        print(f"是否相似 (阈值0.8): {is_similar}")
        
        # 提供相似度解释
        print_explanation(similarity)
    else:
        # 对比模式：显示所有方法的结果
        print("计算方法: 所有方法对比")
        
        print("\n=== 不同方法对比 ===")
        methods = QuickMatcher.get_scorers_meta()[0].keys()
        
        results = []
        for m in methods:
            score = matcher.calculate_similarity(text1, text2, m)
            similar = matcher.is_similar(text1, text2, m)
            results.append((m, score, similar))
            print(f"  {m:18s}: {score:.4f} (相似: {similar})")
        
        # 找出最高分数的方法
        best_method, best_score, best_similar = max(results, key=lambda x: x[1])
        
        print(f"\n=== 推荐结果 ===")
        print(f"最佳方法: {best_method}")
        print(f"最高分数: {best_score:.4f}")
        print(f"是否相似: {best_similar}")
        
        # 提供相似度解释（基于最佳分数）
        print_explanation(best_score)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='测试QuickMatcher类')
    parser.add_argument('--mode', choices=['unit', 'interactive', 'both', 'custom'], default='custom',
                       help='测试模式: unit(单元测试), interactive(交互式测试), both(两者都运行), custom(自定义文本相似度计算)')
    parser.add_argument('-v', '--verbose', action='store_true', help='启用详细日志')
    parser.add_argument('--text1', type=str, help='第一个文本 (仅在custom模式下使用)')
    parser.add_argument('--text2', type=str, help='第二个文本 (仅在custom模式下使用)')
    parser.add_argument('--method', type=str, default=None, 
                       choices = QuickMatcher.get_scorers_meta()[0].keys(),
                       help='相似度计算方法 (仅在custom模式下使用)，不指定则对比所有方法')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.info("Verbose logging enabled.")
    logging.config.dictConfig(get_logging_config(cli_verbose_flag=args.verbose))
    
    success = True
    
    if args.mode in ['unit', 'both']:
        print("运行单元测试...")
        # 运行单元测试
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromTestCase(TestQuickMatcher)
        runner = unittest.TextTestRunner(verbosity=2 if args.verbose else 1)
        result = runner.run(suite)
        
        if not result.wasSuccessful():
            success = False
            print(f"\n❌ 单元测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        else:
            print("\n✅ 所有单元测试通过")
    
    if args.mode in ['interactive', 'both']:
        if args.mode == 'both':
            print("\n" + "="*50)
        
        try:
            run_interactive_test()
            print("\n✅ 交互式测试完成")
        except Exception as e:
            success = False
            print(f"\n❌ 交互式测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    if args.mode == 'custom':
        if not args.text1 or not args.text2:
            print("❌ 自定义模式需要提供 --text1 和 --text2 参数")
            print("示例: python tests/test_quick_matcher.py --mode custom --text1 \"您好，请问如何修改密码？\" --text2 \"你好，怎么改密码呢？\"")
            success = False
        else:
            try:
                run_custom_similarity_test(args.text1, args.text2, args.method)
                print("\n✅ 自定义相似度计算完成")
            except Exception as e:
                success = False
                print(f"\n❌ 自定义相似度计算失败: {e}")
                import traceback
                traceback.print_exc()
    
    if success:
        print("\n🎉 所有测试通过！QuickMatcher类工作正常。")
    else:
        print("\n💥 测试失败，请检查代码。")
        sys.exit(1)