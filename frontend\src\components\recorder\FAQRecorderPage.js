import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import CONFIG from '../../config';
import { generateRequestIdWithPrefix } from '../../utils/requestId';
import ChannelServiceSelector from './ChannelServiceSelector';
import QuestionAnswerForm from './QuestionAnswerForm';
import AIRecommendations from './AIRecommendations';
import CategorySelector from './CategorySelector';
import QualityCheckResults from './QualityCheckResults';
import SubmitConfirmation from './SubmitConfirmation';

function FAQRecorderPage() {
  // 基础配置状态
  const [channel, setChannel] = useState('');
  const [service, setService] = useState('google');
  
  // 问答数据状态
  const [qaData, setQAData] = useState({
    question: '',
    answer: '',
    questionExample: ''
  });
  
  // AI分析结果状态
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  // 分类选择状态
  const [selectedCategoryPath, setSelectedCategoryPath] = useState([]);
  const [categoryTree, setCategoryTree] = useState([]);
  
  // 质量检查状态
  //const [qualityCheckResults, setQualityCheckResults] = useState(null);
  
  // 提交状态
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitResults, setSubmitResults] = useState(null);

  // 加载分类结构
  const loadCategoryStructure = useCallback(async () => {
    try {
      console.log('Loading category structure for channel:', channel);
      
      const requestId = generateRequestIdWithPrefix('recorder');
      const response = await axios.post(`${CONFIG.API_BASE_URL}/faq_recorder/categories`, {
        channel: channel || null,
        max_depth: -1
      }, {
        headers: {
          'x-request-id': requestId,
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.response_code === 200) {
        setCategoryTree(response.data.category_tree || []);
        console.log('Category structure loaded:', response.data.total_categories, 'categories');
      } else {
        console.error('Failed to load categories:', response.data?.response_text);
        setCategoryTree([]);
      }
    } catch (error) {
      console.error('Error loading category structure:', error);
      setCategoryTree([]);
    }
  }, [channel]);

  // 渠道变化时重新加载分类结构
  useEffect(() => {
    loadCategoryStructure();
  }, [loadCategoryStructure]);

  // 处理AI分析
  const handleAnalyze = async () => {
    if (!qaData.question.trim() || !qaData.answer.trim()) {
      alert('请填写问题和答案');
      return;
    }
    
    setIsAnalyzing(true);
    setAnalysisResults(null); // 清除之前的结果
    
    try {
      console.log('Analyzing QA pair:', qaData);
      
      const requestId = generateRequestIdWithPrefix('recorder');
      const response = await axios.post(`${CONFIG.API_BASE_URL}/faq_recorder/analyze`, {
        qa_pair: {
          question: qaData.question,
          answer: qaData.answer,
          question_example: qaData.questionExample || null
        },
        channel: channel || null,
        service: service
      }, {
        headers: {
          'x-request-id': requestId,
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.response_code === 200) {
        setAnalysisResults({
          recommended_categories: response.data.recommended_categories || [],
          quality_check: response.data.quality_check || null,
          examine_result: response.data.examine_result || null
        });
        console.log('Analysis completed successfully:', response.data);
      } else {
        console.error('Analysis failed:', response.data?.response_text);
        alert('AI分析失败: ' + (response.data?.response_text || '未知错误'));
      }
    } catch (error) {
      console.error('Analysis error:', error);
      alert('AI分析出错: ' + (error.response?.data?.detail || error.message));
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 处理分类选择
  const handleCategorySelect = (categoryPath) => {
    setSelectedCategoryPath(categoryPath);
  };

  // 处理最终提交
  const handleSubmit = async () => {
    if (!selectedCategoryPath.length) {
      alert('请选择分类路径');
      return;
    }
    
    setIsSubmitting(true);
    setSubmitResults(null);
    
    try {
      console.log('Submitting FAQ entry:', {
        qaData,
        categoryPath: selectedCategoryPath,
        channel
      });
      
      const requestId = generateRequestIdWithPrefix('recorder');
      const response = await axios.post(`${CONFIG.API_BASE_URL}/faq_recorder/submit`, {
        qa_pair: {
          question: qaData.question,
          answer: qaData.answer,
          question_example: qaData.questionExample || null
        },
        category_path: selectedCategoryPath,
        channel: channel || null
      }, {
        headers: {
          'x-request-id': requestId,
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.response_code === 200) {
        setSubmitResults({
          success: true,
          message: response.data.response_text || 'FAQ录入成功',
          data: response.data.submitted_data || null
        });
        console.log('Submit completed successfully');
      } else {
        setSubmitResults({
          success: false,
          message: response.data?.response_text || '提交失败'
        });
        console.error('Submit failed:', response.data?.response_text);
      }
    } catch (error) {
      console.error('Submit error:', error);
      setSubmitResults({
        success: false,
        message: error.response?.data?.detail || error.message || '提交出错'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="recorder-page">
      <div className="page-header">
        <h1>📝 FAQ智能录入</h1>
        <p className="page-description">
          使用AI辅助快速录入FAQ，自动推荐分类路径并进行质量检查
        </p>
      </div>

      <div className="recorder-sections">
        {/* 渠道和服务选择 */}
        <section className="section channel-service-section">
          <h2>🎯 渠道和服务配置</h2>
          <ChannelServiceSelector
            channel={channel}
            service={service}
            onChannelChange={setChannel}
            onServiceChange={setService}
          />
        </section>

        {/* 问答输入 */}
        <section className="section qa-input-section">
          <h2>✍️ 问答内容输入</h2>
          <QuestionAnswerForm
            qaData={qaData}
            onChange={setQAData}
            onAnalyze={handleAnalyze}
            isAnalyzing={isAnalyzing}
            examineResult={analysisResults?.examine_result}
          />
        </section>

        {/* AI推荐结果 */}
        {analysisResults && (
          <section className="section ai-recommendations-section">
            <h2>🤖 AI智能推荐</h2>
            <AIRecommendations
              recommendations={analysisResults.recommended_categories}
              onSelect={handleCategorySelect}
              selectedPath={selectedCategoryPath}
            />
          </section>
        )}

        {/* 分类选择器 */}
        <section className="section category-selector-section">
          <h2>🗂️ 分类路径选择</h2>
          <CategorySelector
            categoryTree={categoryTree}
            selectedPath={selectedCategoryPath}
            onSelect={handleCategorySelect}
            channel={channel}
            recommendations={analysisResults?.recommended_categories || []}
            onRefresh={loadCategoryStructure}
          />
        </section>

        {/* 质量检查结果 */}
        {analysisResults?.quality_check && (
          <section className="section quality-check-section">
            <h2>🔍 质量检查结果</h2>
            <QualityCheckResults
              qualityCheck={analysisResults.quality_check}
            />
          </section>
        )}

        {/* 提交确认 */}
        {selectedCategoryPath.length > 0 && (
          <section className="section submit-confirmation-section">
            <h2>✅ 提交确认</h2>
            <SubmitConfirmation
              qaData={qaData}
              categoryPath={selectedCategoryPath}
              channel={channel}
              qualityCheck={analysisResults?.quality_check}
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
              submitResults={submitResults}
            />
          </section>
        )}
      </div>
    </div>
  );
}

export default FAQRecorderPage;