import * as XLSX from 'xlsx';

/**
 * 解析Excel文件内容
 * @param {ArrayBuffer} data - 从文件读取的ArrayBuffer数据
 * @returns {Promise<Array<Object>>} - 解析后的JSON对象数组
 */
export const parseExcelFile = (data) => {
  return new Promise((resolve, reject) => {
    try {
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error("Excel文件中没有找到工作表。");
      }
      const worksheet = workbook.Sheets[sheetName];
      const json = XLSX.utils.sheet_to_json(worksheet);

      // 预校验，确保至少包含 question 和 golden_answer 列
      if (json.length > 0) {
        if (!json[0].hasOwnProperty('question')) {
          throw new Error("文件必须包含 'question' 列。");
        }
        if (!json[0].hasOwnProperty('golden_answer')) {
          throw new Error("文件必须包含 'golden_answer' 列。");
        }
      }

      resolve(json);
    } catch (error) {
      console.error("文件解析失败:", error);
      reject(error);
    }
  });
};
