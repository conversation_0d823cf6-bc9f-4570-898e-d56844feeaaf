# AI 聊天应用前端 Docker 镜像
# 多阶段构建：阶段1构建React应用，阶段2使用Nginx提供静态文件服务

# ==================== 阶段 1: 构建阶段 ====================
FROM node:20-alpine AS build

# 配置Alpine包管理器使用清华源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories

# 安装envsubst工具（在构建阶段安装，后续复制到生产镜像）
RUN apk add --no-cache gettext

# 配置npm使用淘宝镜像源
RUN npm config set registry https://registry.npmmirror.com

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json（如果存在）
COPY package*.json ./

# 安装依赖（包含devDependencies，构建时需要）
RUN npm ci

# 复制源代码
COPY . .

# 构建React应用（不注入环境变量，使用运行时配置）
RUN npm run build

# ==================== 阶段 2: 生产阶段 ====================
FROM nginx:alpine

# 配置Alpine包管理器使用清华源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories

# 从构建阶段复制envsubst工具
COPY --from=build /usr/bin/envsubst /usr/bin/envsubst

# 复制Nginx配置模板和启动脚本
COPY product/nginx.conf.template /etc/nginx/nginx.conf.template
COPY product/config.template.js /usr/share/nginx/html/config.template.js
COPY product/entrypoint.sh /entrypoint.sh

# 复制构建好的React应用到Nginx默认目录
COPY --from=build /app/build /usr/share/nginx/html

# 设置启动脚本执行权限
RUN chmod +x /entrypoint.sh

# 暴露端口80
EXPOSE 80

# 使用自定义启动脚本
CMD ["/entrypoint.sh"]
