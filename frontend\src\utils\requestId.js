/**
 * 请求ID生成工具
 * 用于生成前端请求的唯一标识符
 */

/**
 * 生成UUID v4格式的请求ID
 * @returns {string} UUID格式的请求ID
 */
export function generateRequestId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : ((r & 0x3) | 0x8);
    return v.toString(16);
  });
}

/**
 * 生成带前缀的请求ID
 * @param {string} prefix - 前缀，如 'chat', 'faq', 'user'
 * @returns {string} 带前缀的请求ID
 */
export function generateRequestIdWithPrefix(prefix = 'req') {
  const uuid = generateRequestId();
  return `${prefix}-${uuid}`;
}

/**
 * 验证请求ID格式是否正确
 * @param {string} requestId - 要验证的请求ID
 * @returns {boolean} 是否为有效的UUID格式
 */
export function isValidRequestId(requestId) {
  if (!requestId || typeof requestId !== 'string') {
    return false;
  }
  
  // 移除可能的前缀，只验证UUID部分
  const uuidPart = requestId.includes('-') ? requestId.split('-').slice(-5).join('-') : requestId;
  
  // UUID v4 正则表达式
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuidPart);
}

/**
 * 从完整的请求ID中提取UUID部分
 * @param {string} requestId - 完整的请求ID
 * @returns {string} UUID部分
 */
export function extractUuidFromRequestId(requestId) {
  if (!requestId) return '';
  
  // 如果包含前缀，提取UUID部分
  if (requestId.includes('-') && requestId.split('-').length > 5) {
    return requestId.split('-').slice(-5).join('-');
  }
  
  return requestId;
}
