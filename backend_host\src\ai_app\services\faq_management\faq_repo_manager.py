"""
FAQ仓库管理器

负责管理FAQ数据的加载、存储和查询，支持通用数据表和多channel的增量数据。
"""

import json
from typing import Dict, List, Any, Optional, Tuple
from ai_app.shared.exceptions import FAQRepoError, ExcelLoadError, FAQRepoNotFoundError
from ai_app.shared.request_tracing import get_traced_logger
from ai_app.services.faq_management.excel_loader import ExcelLoader
from ai_app.services.faq_management.data_parser import FAQDataParser
from ai_app.shared.excel_utils import DEFAULT_SHEET_NAME
from ai_app.config import config

logger = get_traced_logger(__name__)

class FAQRepoManager:
    """
    FAQ仓库管理器，负责加载、管理和查询FAQ数据。
    
    数据结构：
    - common: 通用数据表（第一张Excel表）
    - channels: 各channel的数据（后续Excel表与通用表合并后的结果）
    """
    
    def __init__(self, excel_path: str):
        """
        初始化FAQ仓库管理器。
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.excel_loader = ExcelLoader(excel_path)
        self.faq_data: Dict[str, List[Dict[str, Any]]] = {}
        self._loaded = False
    
    def load_from_excel(self) -> bool:
        """
        从Excel文件加载所有sheet数据到内存。
        
        Returns:
            是否加载成功
        """
        try:
            logger.debug("Starting to load FAQ data from Excel file")
            
            # 验证Excel文件结构
            if not self.excel_loader.validate_excel_structure():
                raise FAQRepoError("Excel file structure validation failed")
            
            # 加载所有sheet数据
            self.faq_data = self.excel_loader.load_all_sheets_to_memory()
            
            if not self.faq_data:
                raise FAQRepoError("No FAQ data loaded")
            
            if DEFAULT_SHEET_NAME not in self.faq_data:
                raise FAQRepoError("Missing common data sheet")
            
            self._loaded = True
            
            # 统计信息
            logger.debug(f"FAQ data loading completed:")
            logger.debug(f"  - Common data sheet: {len(self.faq_data[DEFAULT_SHEET_NAME])} root categories")
            logger.debug(f"  - Available special channels: {self.get_available_channels()}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load FAQ data: {e}")
            self._loaded = False
            return False
    
    def reload_data(self) -> bool:
        """
        重新加载数据，支持热更新。
        
        Returns:
            是否重新加载成功
        """
        logger.debug("Reloading FAQ data")
        self._loaded = False
        self.faq_data.clear()
        return self.load_from_excel()
    
    def is_loaded(self) -> bool:
        """
        检查数据是否已加载。
        
        Returns:
            是否已加载
        """
        return self._loaded
    
    def get_faq_data(self, channel: Optional[str] = None) -> Tuple[List[Dict[str, Any]], str]:
        """
        获取FAQ数据。
        
        Args:
            channel: 渠道名称，为None时返回通用数据表
            
        Returns:
            FAQ数据列表，真正的channel名称
            
        Raises:
            FAQRepoError: 当数据未加载时
            FAQRepoNotFoundError: 当channel不存在时
        """
        if not self._loaded:
            raise FAQRepoError("FAQ data not loaded yet, please call load_from_excel() first")
        
        if channel is None:
            return self.faq_data.get(DEFAULT_SHEET_NAME, []), DEFAULT_SHEET_NAME
        
        if channel not in self.faq_data:
            available_channels = self.get_available_channels()
            raise FAQRepoNotFoundError(f"Channel '{channel}' does not exist, available channels: {available_channels}")
        
        return self.faq_data[channel], channel
    
    def get_available_channels(self) -> List[str]:
        """
        获取所有可用的channel列表。
        
        Returns:
            channel名称列表（不包含'common'）
        """
        if not self._loaded:
            return []
        
        return [k for k in self.faq_data.keys() if k != DEFAULT_SHEET_NAME]
    
    def get_data_stats(self) -> Dict[str, Any]:
        """
        获取数据统计信息。
        
        Returns:
            统计信息字典
        """
        if not self._loaded:
            return {
                "loaded": False,
                "total_datasets": 0,
                "available_channels": [],
                "common_categories": 0,
                "total_categories": 0
            }
        
        channels = self.get_available_channels()
        common_categories = len(self.faq_data.get(DEFAULT_SHEET_NAME, []))
        total_categories = sum(len(data) for data in self.faq_data.values())
        
        return {
            "loaded": True,
            "excel_path": self.excel_path,
            "total_datasets": len(self.faq_data),
            "available_channels": channels,
            "common_categories": common_categories,
            "total_categories": total_categories,
            "channel_stats": {
                channel: len(data) for channel, data in self.faq_data.items()
            }
        }
    
    def has_channel(self, channel: str) -> bool:
        """
        检查指定channel是否存在。

        Args:
            channel: 渠道名称

        Returns:
            是否存在
        """
        return self._loaded and channel in self.faq_data

    def create_parser(self, channel: Optional[str] = None, allow_fallback: bool = False):
        """
        根据channel名称创建FAQDataParser实例。

        Args:
            channel: 渠道名称，为None时使用通用数据表

        Returns:
            FAQDataParser实例

        Raises:
            FAQRepoError: 当数据未加载或channel不存在时
        """

        try:
            faq_data, real_channel = self.get_faq_data(channel)
        except FAQRepoNotFoundError:
            if allow_fallback:
                faq_data, real_channel = self.get_faq_data(DEFAULT_SHEET_NAME)
            else:
                raise
        return FAQDataParser(faq_data=faq_data, channel=real_channel)


# 全局FAQ仓库管理器实例
_faq_repo_manager: Optional[FAQRepoManager] = None


def initialize_faq_repo_manager(excel_path: str) -> FAQRepoManager:
    """
    初始化全局FAQ仓库管理器。
    
    Args:
        excel_path: Excel文件路径
        
    Returns:
        FAQ仓库管理器实例
        
    Raises:
        FAQRepoError: 当初始化失败时
    """
    global _faq_repo_manager
    
    try:
        _faq_repo_manager = FAQRepoManager(excel_path)
        if not _faq_repo_manager.load_from_excel():
            raise FAQRepoError("FAQ repository manager initialization failed")
        
        logger.info("Global FAQ repository manager initialized successfully")
        logger.debug(f"FAQ repo data stats: {json.dumps(_faq_repo_manager.get_data_stats(), indent=2)}")
        return _faq_repo_manager
        
    except Exception as e:
        logger.error(f"Failed to initialize FAQ repository manager: {e}")
        _faq_repo_manager = None
        raise FAQRepoError(f"Initialization failed: {e}") from e


def get_faq_repo_manager(try_init: bool = False) -> FAQRepoManager:
    """
    获取全局FAQ仓库管理器实例。
    
    Returns:
        FAQ仓库管理器实例
        
    Raises:
        FAQRepoError: 当管理器未初始化时
    """
    if _faq_repo_manager is None:
        if try_init:
            return initialize_faq_repo_manager(config.faq_excel_path)
        else:
            raise FAQRepoError("FAQ repository manager not initialized yet, please call initialize_faq_repo_manager() first")
    
    return _faq_repo_manager


def cleanup_faq_repo_manager():
    """
    清理全局FAQ仓库管理器。
    """
    global _faq_repo_manager
    if _faq_repo_manager is not None:
        logger.debug("Cleaning up FAQ repository manager")
        _faq_repo_manager = None
