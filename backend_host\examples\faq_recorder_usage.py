#!/usr/bin/env python3
"""
FAQ Recorder Agent 使用示例

演示如何使用FAQ录入代理进行智能FAQ录入。
"""

import asyncio
import os
import sys

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from src.ai_app.agents.faq_recorder_agent.agent import FAQRecorderAgent
from src.ai_app.models.faq_recorder import FAQRecorderQuestionAnswer


async def demo_basic_usage():
    """基础使用演示"""
    print("=" * 60)
    print("FAQ Recorder Agent - 基础使用演示")
    print("=" * 60)
    
    # 1. 初始化代理
    print("1. 初始化FAQ录入代理...")
    agent = FAQRecorderAgent(
        channel="demo",
        model_platform="volcano"  # 或者 "bailian"
    )
    
    # 2. 检查状态
    status = agent.get_workflow_status()
    print(f"代理状态: {'正常' if status.get('workflow_available') else '异常'}")
    
    # 3. 获取FAQ结构
    print("\n2. 获取FAQ分类结构...")
    faq_structure = await agent.get_faq_structure(max_depth=3)
    if faq_structure.get("success"):
        print("FAQ分类结构获取成功")
        # 显示前几行结构
        lines = faq_structure["structure_markdown"].split('\n')[:10]
        for line in lines:
            if line.strip():
                print(f"  {line}")
        print("  ...")
    
    # 4. 分类推荐演示
    print("\n3. 测试分类推荐功能...")
    
    test_qa = {
        "question": "充值后游戏币没有到账怎么办？",
        "answer": "如果充值后游戏币没有到账，请按以下步骤处理：1. 等待2-5分钟，系统可能存在延迟；2. 重新登录游戏检查；3. 查看充值记录确认订单状态；4. 如仍未解决，请联系在线客服并提供订单号。"
    }
    
    recommendations = await agent.get_category_recommendations(
        question=test_qa["question"],
        answer=test_qa["answer"],
        top_k=3
    )
    
    if recommendations.get("success"):
        print(f"获得 {len(recommendations['recommendations'])} 个分类推荐:")
        for i, rec in enumerate(recommendations["recommendations"], 1):
            path = " > ".join(rec["category_path"])
            score = rec["combined_score"]
            print(f"  {i}. {path} (评分: {score:.3f})")
    
    # 5. 质量验证演示
    print("\n4. 测试质量验证功能...")
    
    quality_result = await agent.validate_quality(
        question=test_qa["question"],
        answer=test_qa["answer"]
    )
    
    if quality_result.get("success"):
        assessment = quality_result["quality_assessment"]
        print(f"质量评估结果:")
        print(f"  评分: {assessment['score']:.1f} ({assessment['grade']})")
        print(f"  可接受: {'是' if assessment['is_acceptable'] else '否'}")
        print(f"  问题数量: {len(assessment['issues'])}")
        print(f"  重复检测: 发现 {len(quality_result['duplicates'])} 个相似项")
        
        if assessment['issues']:
            print("  主要问题:")
            for issue in assessment['issues'][:3]:
                print(f"    - {issue}")
    
    # 6. 完整工作流演示
    print("\n5. 执行完整的FAQ录入工作流...")
    
    qa_pair = FAQRecorderQuestionAnswer(
        question=test_qa["question"],
        answer=test_qa["answer"]
    )
    
    print(f"处理FAQ: {qa_pair.question[:50]}...")
    
    try:
        # 这里应该使用具体的agent方法，而不是process_user_request
        print("注意：此演示需要实际的处理方法实现")
        print("✓ FAQ录入演示完成!")
    
    except Exception as e:
        print(f"✗ 处理过程中出现异常: {e}")
    
    print("\n演示完成!")


async def demo_batch_processing():
    """批量处理演示"""
    print("\n" + "=" * 60)
    print("FAQ Recorder Agent - 批量处理演示")
    print("=" * 60)
    
    agent = FAQRecorderAgent(
        channel="batch_demo",
        model_platform="volcano"
    )
    
    # 批量FAQ数据
    faq_batch = [
        {
            "question": "如何查看游戏排行榜？",
            "answer": '在游戏主界面点击"排行榜"按钮，可以查看各种排行榜，包括等级榜、战力榜、财富榜等。'
        },
        {
            "question": "怎样加入公会？",
            "answer": '达到15级后可以加入公会。点击"公会"按钮，选择合适的公会并申请加入，等待会长或管理员审批通过。'
        },
        {
            "question": "游戏卡顿怎么解决？",
            "answer": "游戏卡顿可能由以下原因造成：1. 设备性能不足；2. 网络连接不稳定；3. 游戏版本过旧。建议关闭后台应用、检查网络、更新游戏版本。"
        }
    ]
    
    print(f"开始处理 {len(faq_batch)} 条FAQ...")
    
    success_count = 0
    total_time = 0
    
    for i, faq in enumerate(faq_batch, 1):
        print(f"\n处理第 {i} 条: {faq['question'][:30]}...")
        
        try:
            # 先获取推荐
            recommendations = await agent.get_category_recommendations(
                question=faq["question"],
                answer=faq["answer"],
                top_k=1
            )
            
            if recommendations.get("success") and recommendations["recommendations"]:
                top_rec = recommendations["recommendations"][0]
                path = " > ".join(top_rec["category_path"])
                print(f"  推荐分类: {path}")
            
            # 执行录入（实际项目中可能需要人工确认）
            qa_pair = FAQRecorderQuestionAnswer(
                question=faq["question"],
                answer=faq["answer"]
            )
            
            # 这里应该使用具体的agent方法
            print("  注意：此演示需要实际的处理方法实现")
            print(f"  ✓ 录入演示完成")
            success_count += 1
        
        except Exception as e:
            print(f"  ✗ 处理异常: {e}")
    
    print(f"\n批量处理完成:")
    print(f"  成功: {success_count}/{len(faq_batch)}")
    print(f"  总耗时: {total_time:.2f} 秒")
    print(f"  平均耗时: {total_time/max(success_count, 1):.2f} 秒/条")


async def demo_interactive_mode():
    """交互模式演示"""
    print("\n" + "=" * 60)
    print("FAQ Recorder Agent - 交互模式演示")
    print("=" * 60)
    
    agent = FAQRecorderAgent(
        channel="interactive",
        model_platform="volcano"
    )
    
    print("进入交互模式，输入 'quit' 退出")
    print("请输入FAQ问题和答案：")
    
    while True:
        try:
            print("\n" + "-" * 40)
            question = input("问题: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                break
            
            if not question:
                print("问题不能为空，请重新输入")
                continue
            
            answer = input("答案: ").strip()
            
            if not answer:
                print("答案不能为空，请重新输入")
                continue
            
            print("\n分析中...")
            
            # 获取推荐
            recommendations = await agent.get_category_recommendations(
                question=question,
                answer=answer,
                top_k=3
            )
            
            if recommendations.get("success"):
                print("\n推荐分类:")
                for i, rec in enumerate(recommendations["recommendations"], 1):
                    path = " > ".join(rec["category_path"])
                    score = rec["combined_score"]
                    print(f"  {i}. {path} (评分: {score:.3f})")
            
            # 质量检查
            quality = await agent.validate_quality(question, answer)
            
            if quality.get("success"):
                assessment = quality["quality_assessment"]
                print(f"\n质量评估: {assessment['score']:.1f}分 ({assessment['grade']})")
                
                if assessment['issues']:
                    print("注意事项:")
                    for issue in assessment['issues'][:3]:
                        print(f"  - {issue}")
            
            # 询问是否执行录入
            confirm = input("\n是否执行录入? (y/n): ").strip().lower()
            
            if confirm in ['y', 'yes']:
                qa_pair = FAQRecorderQuestionAnswer(
                    question=question,
                    answer=answer
                )
                
                # 这里应该使用具体的agent方法
                print("注意：此演示需要实际的处理方法实现")
                print("✓ 录入演示完成!")
            else:
                print("已取消录入")
        
        except KeyboardInterrupt:
            print("\n用户中断，退出交互模式")
            break
        
        except Exception as e:
            print(f"处理出错: {e}")
    
    print("交互模式结束")


async def main():
    """主函数"""
    print("FAQ Recorder Agent 使用示例\n")
    
    demos = [
        ("基础功能演示", demo_basic_usage),
        ("批量处理演示", demo_batch_processing),
        ("交互模式演示", demo_interactive_mode)
    ]
    
    for i, (name, demo_func) in enumerate(demos, 1):
        print(f"{i}. {name}")
    
    try:
        choice = input("\n请选择演示模式 (1-3): ").strip()
        
        if choice == "1":
            await demo_basic_usage()
        elif choice == "2":
            await demo_batch_processing()
        elif choice == "3":
            await demo_interactive_mode()
        else:
            print("无效选择，运行基础演示...")
            await demo_basic_usage()
    
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    
    except Exception as e:
        print(f"演示执行出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())