# AI智能客服系统

## 项目概述
基于React和FastAPI实现的企业级AI智能客服系统，集成了多AI平台支持、FAQ筛选代理、FAQ录入代理、批量测试等完整功能。系统支持中英双语，采用微服务架构设计，可作为多个AI平台（百炼、Coze、火山方舟、Google等）的统一代理服务。

## 技术栈

### 前端
- **React 19.1.0** - 现代化的用户界面框架
- **React Router 7.6.2** - 单页应用路由管理
- **Axios** - HTTP客户端
- **React Scripts** - 构建工具
- **XLSX** - Excel文件处理
- **React Dropzone** - 文件拖拽上传
- **String Similarity** - 文本相似度计算

### 后端
- **FastAPI** - 高性能异步Web框架
- **Python 3.11** - 编程语言
- **Uvicorn** - ASGI服务器
- **uv** - Python包管理器
- **Pydantic** - 数据验证和序列化
- **httpx** - 异步HTTP客户端
- **Pandas** - 数据处理
- **FuzzyWuzzy** - 模糊字符串匹配
- **Jieba** - 中文分词
- **VolcEngine** - 火山引擎AI服务

### 部署
- **Docker & Docker Compose** - 容器化部署
- **Nginx** - 前端静态文件服务
- **多环境支持** - 开发/测试/生产环境

## 功能特性

### 🤖 核心AI功能
- **智能对话** - 支持多轮对话的AI智能客服系统
- **FAQ筛选代理** - 完整的查询重写→问题分类→向量召回→答案检索→重排序工作流
- **FAQ录入代理** - 智能问答录入工作流，支持问题分析、分类推荐、重复检测、质量评估
- **多平台支持** - 百炼、火山方舟、Coze、Google等多个AI平台
- **向量检索** - 火山向量数据库集成，支持语义搜索

### 📊 数据管理
- **多渠道FAQ** - 支持不同渠道的FAQ数据管理
- **Excel数据源** - 统一的FAQ数据仓库管理器
- **自动数据转换** - Excel↔JSON格式自动转换
- **备份机制** - 数据修改自动备份

### 🧪 测试与验证
- **批量测试** - 支持大规模FAQ测试用例执行
- **黄金测试集** - 内置相似度计算的测试集验证
- **实时监控** - 请求耗时、成功率等指标监控
- **详细日志** - 完整的请求追踪和调试信息

### 🎨 用户界面
- **响应式设计** - 支持桌面和移动端
- **多页面导航** - 聊天、批量测试、FAQ录入页面
- **实时反馈** - 加载状态、错误提示、操作结果反馈
- **多语言支持** - 中英双语界面

## 配置说明

### 1. 环境变量配置
复制示例文件并重命名：
```bash
cp backend_host/.env.example backend_host/.env
cp frontend/.env.example frontend/.env
```

### 2. 后端配置 (`backend_host/.env`)
- **百炼平台**: `BAILIAN_API_BASE`, `BAILIAN_API_KEY`, `BAILIAN_MODEL`
- **火山引擎**: `VOLCANO_API_BASE`, `VOLCANO_API_KEY`, `VOLCANO_MODEL`
- **Coze平台**: `COZE_APP_BASE`, `COZE_API_KEY`, `COZE_WORKFLOW_ID`
- **Google平台**: `GOOGLE_API_BASE`, `GOOGLE_API_KEY`, `GOOGLE_MODEL`
- **向量数据库**: `VOLCANO_KNOWLEDGE_API_BASE`, `VOLCANO_KNOWLEDGE_API_AK`, `VOLCANO_KNOWLEDGE_API_SK`
- **FAQ数据**: `FAQ_EXCEL_PATH` (默认: `./resources/faq_data/faq.xlsx`)
- **提示词路径**: `REWRITE_PROMPT_PATH`, `CLASSIFY_PROMPT_PATH`
- **FAQ录入**: 多个提示词路径配置

### 3. 前端配置 (`frontend/.env`)
- `REACT_APP_API_BASE_URL` - 后端服务地址(默认http://localhost:8000/api/v1)

### 4. Python 版本
项目后端使用 Python 3.11，在 `backend_host/.python-version` 文件中指定。

## 快速开始

### 前端开发
```powershell
cd frontend
npm install
npm start
```

### 后端启动
```powershell
cd backend_host

# 确保已安装 uv (参考官方文档: https://docs.astral.sh/uv/getting-started/installation/)

# 1. 创建并激活虚拟环境
uv venv
#source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate.ps1  # Windows PowerShell

# 2. 同步依赖
uv sync

# 3. 启动后端服务
uv run python -m ai_app.server.main -v
```

## 项目结构

```
project-root/
├── docker/                          # Docker 配置目录
│   ├── backend_host/                # 后端 Dockerfile
│   ├── frontend/                   # 前端 Dockerfile
│   ├── docker-compose.yml          # 服务编排
│   └── README.md                   # Docker 部署文档
├── frontend/                       # React前端
│   ├── src/
│   │   ├── components/
│   │   │   ├── chat/              # 聊天页面组件
│   │   │   ├── batch/             # 批量测试组件
│   │   │   ├── recorder/          # FAQ录入组件
│   │   │   └── common/            # 通用组件
│   │   └── ...
│   ├── package.json
│   └── README.md
├── backend_host/                  # FastAPI后端
│   ├── src/ai_app/
│   │   ├── agents/
│   │   │   ├── faq_filter_agent/  # FAQ筛选代理 (3,328+ 行代码)
│   │   │   ├── faq_recorder_agent/# FAQ录入代理 (MVP 90%+ 完成)
│   │   │   └── shared/            # 共享组件
│   │   ├── server/
│   │   │   ├── main.py            # FastAPI 应用入口
│   │   │   └── routers/           # API路由
│   │   ├── models/                # Pydantic数据模型
│   │   ├── services/              # 外部服务集成
│   │   ├── shared/                # 共享组件
│   │   └── config.py              # 配置管理
│   ├── resources/faq_data/        # FAQ数据文件
│   ├── tests/                     # 测试文件
│   └── pyproject.toml             # 项目配置
├── CLAUDE.md                      # Claude Code 开发指南
└── README.md                      # 项目文档
```

## 核心模块

### FAQ筛选代理 (`faq_filter_agent/`)
完整的FAQ筛选工作流，包含：
- 查询重写 (Query Rewrite)
- 问题分类 (Classification) 
- 向量召回 (Retrieval Fallback)
- 答案检索 (Answer Retrieval)
- 重排序 (Reranking)

### FAQ录入代理 (`faq_recorder_agent/`)
智能录入工作流：
- 问题分析 (LLM内容分析)
- 分类推荐 (智能推荐+规则推荐)
- 重复检测 (相似度计算)
- 质量评估 (完整性检查)
- 数据录入 (Excel写入)

### API路由
- `/api/v1/chat/faq_filter` - FAQ筛选接口
- `/api/v1/faq_recorder/analyze` - 问答分析接口
- `/api/v1/faq_recorder/categories` - 分类结构接口
- `/api/v1/faq_recorder/submit` - 录入提交接口

## 测试

### 测试方法

#### 运行测试
```powershell
cd backend_host

# 运行FAQ筛选代理测试
uv run python -m tests.agents.faq_filter_agent.test_faq_management

# 运行FAQ录入代理测试
uv run python -m tests.agents.faq_recorder_agent.test_faq_recorder

```

## Docker化部署

项目支持使用 Docker 和 Docker Compose 进行容器化部署。所有相关配置文件位于 `docker/` 文件夹中。

详细部署说明请参阅：`docker/README.md`

## 注意事项

1. **环境要求**: Node.js (>=16) 和 Python 3.11
2. **包管理**: 后端使用 `uv` 进行包和环境管理
3. **API密钥**: 务必在 `.env` 文件中配置所需的API密钥和URL
4. **数据管理**: FAQ数据通过统一的仓库管理器自动加载
5. **多渠道支持**: 支持不同渠道的FAQ数据文件自动切换
6. **优雅降级**: 分类失败时自动启用向量检索

## 开发指南

### 添加新的AI平台
1. 在 `backend_host/src/ai_app/agents/shared/llm_impl/` 下创建新的实现类
2. 在 `config.py` 中添加配置项
3. 更新相关Agent的初始化逻辑

### 扩展FAQ数据源
1. 修改 `backend_host/src/ai_app/services/faq_management/faq_repo_manager.py`
2. 更新数据解析器支持新格式
3. 配置相应的文件路径

### 自定义工作流
1. 定义新的工作流逻辑
2. 实现相应的节点和状态管理
3. 集成到现有的Agent框架中

## 版本历史

- **v0.1.8** - 添加FAQ录入代理，支持多AI平台，优化Docker部署
- **v0.1.7** - 完善FAQ筛选代理工作流
- **v0.1.6** - 添加批量测试功能，支持黄金测试集验证
- **v0.1.5** - 基础聊天功能，多AI平台支持