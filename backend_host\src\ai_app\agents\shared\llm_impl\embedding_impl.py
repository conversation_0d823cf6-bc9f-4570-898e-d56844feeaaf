import abc
from typing import List, Dict, Any, Tuple
from ai_app.models.chat import ChatModelUsage

class EmbeddingImpl(abc.ABC):
    """与 EMBEDDING 服务交互的抽象基类。"""

    @abc.abstractmethod
    def get_doc_name(self) -> str:
        raise NotImplementedError

    @abc.abstractmethod
    async def search_knowledge(self,
        query: str,
        top_n: int,
    ) -> Tuple[List[Dict[str, Any]], ChatModelUsage, Dict[str, Any]]:
        raise NotImplementedError