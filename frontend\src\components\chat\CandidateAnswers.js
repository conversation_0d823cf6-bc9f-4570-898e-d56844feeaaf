import React from 'react';

// 候选答案组件展示
function CandidateAnswers({ answers, selectedIndex, onSelect, groupName }) {
  if (!answers || answers.length === 0) return null;
  
  return (
    <div className="candidate-answers">
      <div className="candidate-title">候选答案：</div>
      <table className="candidate-table">
        <thead>
          <tr>
            <th className="col-select">选择</th>
            <th className="col-score">分数</th>
            <th className="col-answer">答案</th>
            <th className="col-reason">原因</th>
          </tr>
        </thead>
        <tbody>
          {answers.map((item, index) => (
            <tr key={index} className={index === selectedIndex ? 'selected' : ''}>
              <td>
                <input
                  type="radio"
                  name={groupName}
                  checked={index === selectedIndex}
                  onChange={() => onSelect(index)}
                />
              </td>
              <td className="candidate-score">
                {((item.score || 0) * 100).toFixed(2)}%
              </td>
              <td className="candidate-text">
                {item.content}
              </td>
              <td className="candidate-reason">
                {item.reason || '-'} {/* 如果没有 reason，显示 '-' */}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default CandidateAnswers; 