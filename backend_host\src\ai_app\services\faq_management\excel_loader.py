"""
Excel加载器

负责从Excel文件动态加载所有sheet数据，支持通用数据表和增量表的合并处理。
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from ai_app.shared.exceptions import ExcelLoadError
from ai_app.shared.excel_utils import ExcelImporter, get_excel_sheet_names, read_excel_file, DEFAULT_SHEET_NAME
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)


class ExcelLoader:
    """
    Excel文件加载器，支持动态加载所有sheet数据。
    - 第一张表作为通用数据表（相当于原faq_doc.json）
    - 后续每张表作为增量表，与通用数据表合并（相当于原faq_doc-{channel}.json）
    """
    
    def __init__(self, excel_path: str):
        """
        初始化Excel加载器。
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.excel_reader = ExcelImporter(excel_path)
    
    def load_all_sheets_to_memory(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        加载Excel文件所有sheet并返回内存中的JSON格式数据。
        
        Returns:
            dict: {
                'common': [...],  # 第一张表的数据（通用数据表）
                'channel1': [...],  # 第二张表与通用数据表合并后的数据
                'channel2': [...],  # 第三张表与通用数据表合并后的数据
                ...
            }
        
        Raises:
            ExcelLoadError: 当Excel文件加载失败时
        """
        try:
            logger.debug(f"Starting to load Excel file: {self.excel_path}")
            
            # 获取所有sheet名称
            sheet_names = get_excel_sheet_names(self.excel_path)
            logger.debug(f"Found {len(sheet_names)} sheets: {sheet_names}")
            
            if not sheet_names:
                raise ExcelLoadError("No sheets found in Excel file")
            
            result = {}
            
            # 第一张表作为通用数据表
            common_sheet_name = DEFAULT_SHEET_NAME
            logger.debug(f"Loading common data sheet: {common_sheet_name}")
            common_data = self.excel_reader.read_excel(sheet_name=common_sheet_name)
            
            if common_data is None:
                raise ExcelLoadError(f"Failed to load common data sheet: {common_sheet_name}")
            
            result[common_sheet_name] = common_data
            logger.debug(f"Common data sheet loaded with {len(common_data)} root categories")
            
            # 处理后续的增量表
            for i, sheet_name in enumerate(sheet_names, 1):
                if sheet_name == common_sheet_name:
                    continue
                logger.debug(f"Loading incremental sheet {i}: {sheet_name}")
                
                try:
                    # 使用通用数据表作为基础，合并增量表
                    merged_data = self.excel_reader.read_excel(
                        sheet_name=sheet_name, 
                        base_sheet_name=common_sheet_name
                    )
                    
                    if merged_data is None:
                        logger.warning(f"Failed to load incremental sheet {sheet_name}, skipping")
                        continue
                    
                    # 使用sheet名称作为channel名称
                    channel_name = sheet_name
                    result[channel_name] = merged_data
                    logger.debug(f"Incremental sheet {sheet_name} loaded, channel: {channel_name}, contains {len(merged_data)} root categories")
                    
                except Exception as e:
                    logger.warning(f"Error loading incremental sheet {sheet_name}: {e}, skipping")
                    continue
            
            logger.info(f"Excel file loading completed, loaded {len(result)} datasets: {list(result.keys())}")
            return result
            
        except FileNotFoundError:
            error_msg = f"Excel file not found: {self.excel_path}"
            logger.error(error_msg)
            raise ExcelLoadError(error_msg)
        except Exception as e:
            error_msg = f"Error loading Excel file: {e}"
            logger.error(error_msg)
            raise ExcelLoadError(error_msg) from e
    
    def validate_excel_structure(self) -> bool:
        """
        验证Excel文件结构是否符合要求。
        
        Returns:
            是否符合要求
        """
        try:
            # 检查第一张表（通用数据表）
            common_sheet = read_excel_file(self.excel_path)
            if common_sheet.empty:
                logger.error("Common data sheet is empty")
                return False
            
            # 检查列数（应该至少有7列：5个分类列 + 答案 + 问题示例）
            if len(common_sheet.columns) < 7:
                logger.warning(f"Insufficient columns in common data sheet, expected at least 7 columns, actual {len(common_sheet.columns)} columns")
            
            logger.debug(f"Excel file structure validation passed, contains {len(common_sheet)} rows in common data sheet")
            return True
            
        except Exception as e:
            logger.error(f"Error validating Excel file structure: {e}")
            return False
