<!-- Generator: Widdershins v4.0.1 -->

<h1 id="fastapi">FastAPI v0.1.0</h1>

> Scroll down for code samples, example requests and responses. Select a language for code samples from the tabs above or the mobile navigation menu.

<h1 id="fastapi-default">Default</h1>

## Chat To Faq Filter

<a id="opIdchat_to_faq_filter_api_v1_chat_faq_filter_post"></a>

> Code samples

```shell
# You can also use wget
curl -X POST /api/v1/chat/faq_filter \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json'

```

```java
URL obj = new URL("/api/v1/chat/faq_filter");
HttpURLConnection con = (HttpURLConnection) obj.openConnection();
con.setRequestMethod("POST");
int responseCode = con.getResponseCode();
BufferedReader in = new BufferedReader(
    new InputStreamReader(con.getInputStream()));
String inputLine;
StringBuffer response = new StringBuffer();
while ((inputLine = in.readLine()) != null) {
    response.append(inputLine);
}
in.close();
System.out.println(response.toString());

```

`POST /api/v1/chat/faq_filter`

处理到 FAQ Filter Agent 的聊天请求。

> Body parameter

```json
{
  "conversation": [
    {
      "role": "string",
      "content": "string"
    }
  ],
  "session_id": "string",
  "service": "string",
  "context_params": {}
}
```

<h3 id="chat-to-faq-filter-parameters">Parameters</h3>

|Name|In|Type|Required|Description|
|---|---|---|---|---|
|body|body|[ChatRequest](#schemachatrequest)|true|none|

> Example responses

> 200 Response

```json
{
  "response_code": 0,
  "response_text": "string",
  "session_id": "string",
  "usages": {
    "models": [
      {
        "model_id": "string",
        "input_tokens": 0,
        "output_tokens": 0
      }
    ]
  },
  "response_body": [
    {
      "content": "string",
      "category_chain": "string",
      "score": 0,
      "reason": "string"
    }
  ],
  "rewritten_query": "string",
  "classify_thinking": "string"
}
```

<h3 id="chat-to-faq-filter-responses">Responses</h3>

|Status|Meaning|Description|Schema|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|Successful Response|[ChatToFaqFilterResponse](#schemachattofaqfilterresponse)|
|422|[Unprocessable Entity](https://tools.ietf.org/html/rfc2518#section-10.3)|Validation Error|[HTTPValidationError](#schemahttpvalidationerror)|

<aside class="success">
This operation does not require authentication
</aside>

# Schemas

<h2 id="tocS_ChatInputMessage">ChatInputMessage</h2>
<!-- backwards compatibility -->
<a id="schemachatinputmessage"></a>
<a id="schema_ChatInputMessage"></a>
<a id="tocSchatinputmessage"></a>
<a id="tocschatinputmessage"></a>

```json
{
  "role": "string",
  "content": "string"
}

```

ChatInputMessage

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|role|string|true|none|消息发送者的角色（例如 'user', 'assistant'）|
|content|string|true|none|消息内容|

<h2 id="tocS_ChatModelUsage">ChatModelUsage</h2>
<!-- backwards compatibility -->
<a id="schemachatmodelusage"></a>
<a id="schema_ChatModelUsage"></a>
<a id="tocSchatmodelusage"></a>
<a id="tocschatmodelusage"></a>

```json
{
  "model_id": "string",
  "input_tokens": 0,
  "output_tokens": 0
}

```

ChatModelUsage

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|model_id|any|false|none|被调用模型的 ID|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|input_tokens|any|false|none|输入消耗的 token 数量|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|integer|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|output_tokens|any|false|none|输出消耗的 token 数量|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|integer|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

<h2 id="tocS_ChatModelUsages">ChatModelUsages</h2>
<!-- backwards compatibility -->
<a id="schemachatmodelusages"></a>
<a id="schema_ChatModelUsages"></a>
<a id="tocSchatmodelusages"></a>
<a id="tocschatmodelusages"></a>

```json
{
  "models": [
    {
      "model_id": "string",
      "input_tokens": 0,
      "output_tokens": 0
    }
  ]
}

```

ChatModelUsages

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|models|any|false|none|包含所有模型调用使用情况的列表|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|[[ChatModelUsage](#schemachatmodelusage)]|false|none|[单个模型调用的 token 使用情况]|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

<h2 id="tocS_ChatRequest">ChatRequest</h2>
<!-- backwards compatibility -->
<a id="schemachatrequest"></a>
<a id="schema_ChatRequest"></a>
<a id="tocSchatrequest"></a>
<a id="tocschatrequest"></a>

```json
{
  "conversation": [
    {
      "role": "string",
      "content": "string"
    }
  ],
  "session_id": "string",
  "service": "string",
  "context_params": {}
}

```

ChatRequest

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|conversation|[[ChatInputMessage](#schemachatinputmessage)]|true|none|包含历史消息的对话列表|
|session_id|any|false|none|可选的会话 ID，用于保持对话上下文(暂时没用)|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|service|any|false|none|可选的后端服务标识（例如 'volcano'（默认）, 'bailian'）|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|context_params|any|false|none|可选的、传递给特定服务的额外上下文参数，比如channel、platform|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|object|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

<h2 id="tocS_ChatToFaqFilterCandidate">ChatToFaqFilterCandidate</h2>
<!-- backwards compatibility -->
<a id="schemachattofaqfiltercandidate"></a>
<a id="schema_ChatToFaqFilterCandidate"></a>
<a id="tocSchattofaqfiltercandidate"></a>
<a id="tocschattofaqfiltercandidate"></a>

```json
{
  "content": "string",
  "category_chain": "string",
  "score": 0,
  "reason": "string"
}

```

ChatToFaqFilterCandidate

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|content|string|true|none|根据分类结果返回的参考答案。分类失败时会包含一个<保底答案>|
|category_chain|string|true|none|问题分类链条，表示上述答案的分类路径。分类失败时字符串为空|
|score|any|false|none|此参考答案的置信度得分（如果可用，取值范围0~1）|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|number|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|reason|any|false|none|AI归为此类的原因依据（如果可用）|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

<h2 id="tocS_ChatToFaqFilterResponse">ChatToFaqFilterResponse</h2>
<!-- backwards compatibility -->
<a id="schemachattofaqfilterresponse"></a>
<a id="schema_ChatToFaqFilterResponse"></a>
<a id="tocSchattofaqfilterresponse"></a>
<a id="tocschattofaqfilterresponse"></a>

```json
{
  "response_code": 0,
  "response_text": "string",
  "session_id": "string",
  "usages": {
    "models": [
      {
        "model_id": "string",
        "input_tokens": 0,
        "output_tokens": 0
      }
    ]
  },
  "response_body": [
    {
      "content": "string",
      "category_chain": "string",
      "score": 0,
      "reason": "string"
    }
  ],
  "rewritten_query": "string",
  "classify_thinking": "string"
}

```

ChatToFaqFilterResponse

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|response_code|integer|true|none|响应码（200为成功，其他均为失败）|
|response_text|any|false|none|错误文本（如果有报错）|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|session_id|any|false|none|本次交互使用的或新生成的会话 ID|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|usages|any|false|none|模型调用的 token 使用情况统计|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|[ChatModelUsages](#schemachatmodelusages)|false|none|多个模型调用的 token 使用情况汇总|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|response_body|any|false|none|包含AI候选回复的列表，优先级从高到低|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|[[ChatToFaqFilterCandidate](#schemachattofaqfiltercandidate)]|false|none|[单个候选回复的结构]|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|rewritten_query|any|false|none|重写后的玩家问题说明|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|classify_thinking|any|false|none|分类思考过程（如果可用）|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|null|false|none|none|

<h2 id="tocS_HTTPValidationError">HTTPValidationError</h2>
<!-- backwards compatibility -->
<a id="schemahttpvalidationerror"></a>
<a id="schema_HTTPValidationError"></a>
<a id="tocShttpvalidationerror"></a>
<a id="tocshttpvalidationerror"></a>

```json
{
  "detail": [
    {
      "loc": [
        "string"
      ],
      "msg": "string",
      "type": "string"
    }
  ]
}

```

HTTPValidationError

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|detail|[[ValidationError](#schemavalidationerror)]|false|none|none|

<h2 id="tocS_ValidationError">ValidationError</h2>
<!-- backwards compatibility -->
<a id="schemavalidationerror"></a>
<a id="schema_ValidationError"></a>
<a id="tocSvalidationerror"></a>
<a id="tocsvalidationerror"></a>

```json
{
  "loc": [
    "string"
  ],
  "msg": "string",
  "type": "string"
}

```

ValidationError

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|loc|[anyOf]|true|none|none|

anyOf

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|string|false|none|none|

or

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|» *anonymous*|integer|false|none|none|

continued

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|msg|string|true|none|none|
|type|string|true|none|none|

