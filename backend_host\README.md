# AI智能客服后端服务

基于FastAPI构建的企业级AI智能客服系统后端，集成了多AI平台支持、FAQ筛选代理、FAQ录入代理、向量检索等核心功能。

## 项目概述

本后端服务采用现代化Python技术栈，使用FastAPI框架提供高性能的RESTful API。系统集成了多个主流AI平台（百炼、火山方舟、Coze、Google等），实现了完整的FAQ筛选工作流和智能FAQ录入功能。

### 核心特性

- **🚀 高性能**: 基于FastAPI和Uvicorn，提供异步处理能力
- **🤖 多AI平台**: 支持百炼、火山方舟、Coze、Google等多个AI平台
- **🧠 智能工作流**: 基于自定义工作流的复杂AI处理流程
- **📊 数据管理**: 统一的FAQ数据仓库管理器，支持Excel和JSON格式
- **🔍 向量检索**: 集成火山向量数据库，支持语义搜索
- **📝 智能录入**: 基于LLM的FAQ智能录入和质量评估
- **🧪 完整测试**: 全面的单元测试和集成测试覆盖

## 技术栈

### 核心框架
- **FastAPI 0.70+** - 现代化的Python Web框架
- **Uvicorn** - ASGI服务器，支持异步处理
- **Pydantic** - 数据验证和序列化
- **Python 3.11** - 推荐Python版本

### AI和机器学习
- **VolcEngine** - 火山引擎SDK
- **FuzzyWuzzy** - 模糊字符串匹配
- **Pandas** - 数据处理和分析
- **Jieba** - 中文分词库

### 工具和库
- **uv** - 现代Python包管理器
- **HTTPX** - 异步HTTP客户端
- **Jinja2** - 模板引擎
- **Python-Dotenv** - 环境变量管理
- **Requests** - HTTP请求库

## 项目结构

```
backend_host/
├── src/ai_app/                    # 主要应用代码目录
│   ├── __init__.py               # 包初始化
│   ├── config.py                 # 配置管理
│   ├── agents/                   # AI代理实现
│   │   ├── __init__.py
│   │   ├── faq_filter_agent/     # FAQ筛选代理 (核心功能)
│   │   │   ├── README.md         # 详细文档
│   │   │   ├── agent.py          # 主协调器
│   │   │   ├── llm_clients.py    # LLM客户端封装
│   │   │   └── data_parser.py    # 数据解析器
│   │   ├── faq_recorder_agent/   # FAQ录入代理 (MVP 90%+ 完成)
│   │   │   ├── PROJECT_PLAN.md   # 项目规划
│   │   │   ├── agent.py          # 主协调器
│   │   │   ├── workflow.py       # LangGraph工作流
│   │   │   ├── llm_clients.py    # LLM客户端
│   │   │   ├── recommender.py    # 分类推荐器
│   │   │   ├── quality_checker.py # 质量检查器
│   │   │   └── excel_writer.py   # Excel写入器
│   │   └── shared/               # 共享组件
│   │       ├── llm_impl/         # LLM平台实现
│   │       │   ├── volcano_impl.py    # 火山引擎实现
│   │       │   ├── bailian_impl.py     # 百炼平台实现
│   │       │   ├── google_impl.py      # Google平台实现
│   │       │   ├── rerank_impl.py      # 重排序实现
│   │       │   └── embedding_impl.py   # 向量嵌入实现
│   │       └── prompts/           # 提示词模板
│   │           ├── common/         # 通用提示词
│   │           ├── faq_filter/     # FAQ筛选提示词
│   │           └── faq_recorder/  # FAQ录入提示词
│   ├── server/                   # FastAPI应用核心
│   │   ├── main.py               # 应用入口
│   │   └── routers/              # API路由
│   │       ├── hello.py          # 健康检查
│   │       ├── chat.py           # 聊天接口
│   │       ├── chat_to_faq_filter.py # FAQ筛选接口
│   │       └── faq_recorder.py   # FAQ录入接口
│   ├── models/                   # Pydantic数据模型
│   │   ├── chat.py               # 聊天相关模型
│   │   ├── chat_to_faq_filter.py # FAQ筛选模型
│   │   ├── faq_recorder.py       # FAQ录入模型
│   │   ├── bailian.py            # 百炼平台模型
│   │   ├── coze.py               # Coze平台模型
│   │   └── common.py             # 通用模型
│   ├── services/                 # 外部服务调用
│   │   ├── bailian.py            # 百炼服务
│   │   ├── coze.py               # Coze服务
│   │   └── faq_management/      # FAQ管理服务
│   │       ├── faq_repo_manager.py    # FAQ仓库管理器
│   │       ├── data_parser.py        # 数据解析器
│   │       └── excel_loader.py        # Excel加载器
│   ├── shared/                   # 共享工具
│   │   ├── excel_utils.py        # Excel工具
│   │   ├── exceptions.py        # 异常定义
│   │   ├── quick_matcher.py      # 快速匹配
│   │   └── request_tracing.py    # 请求追踪
│   └── utils/                    # 工具函数
├── resources/                    # 资源文件
│   └── faq_data/                 # FAQ数据文件
│       ├── faq.xlsx              # 主要FAQ数据 (Excel格式)
│       ├── backups/              # 自动备份目录
│       └── knowledge_lib/        # 知识库文件
├── tests/                        # 测试文件
│   ├── agents/                   # 代理测试
│   │   ├── faq_filter_agent/      # FAQ筛选代理测试
│   │   └── faq_recorder_agent/   # FAQ录入代理测试
│   └── test_quick_matcher.py     # 快速匹配测试
├── pyproject.toml               # 项目配置和依赖管理
├── uv.lock                      # 依赖锁文件
├── .python-version              # Python版本指定
├── .env.example                 # 环境变量模板
└── README.md                    # 后端文档 (本文件)
```

## 核心模块

### 1. FAQ筛选代理 (`faq_filter_agent/`)

完整的FAQ筛选工作流，包含5个核心步骤：

#### 工作流程
1. **查询重写 (Query Rewrite)** - 使用LLM重写用户查询，包含上下文信息
2. **问题分类 (Classification)** - 基于FAQ目录结构进行智能分类
3. **向量召回 (Retrieval Fallback)** - 分类失败时的语义搜索补充
4. **答案检索 (Answer Retrieval)** - 从结构化FAQ数据中获取答案
5. **重排序 (Reranking)** - 可选的答案相关性重排序

#### 核心组件
- `agent.py` - 主协调器，负责工作流编排
- `llm_clients.py` - LLM客户端封装，支持多平台
- `data_parser.py` - FAQ数据解析和目录提取

### 2. FAQ录入代理 (`faq_recorder_agent/`)

智能FAQ录入工作流，MVP已完成90%+。

#### 工作流程
1. **问题分析** - 使用LLM分析问答内容主题和类型
2. **分类推荐** - 智能推荐最合适的分类路径
3. **重复检测** - 检测与现有问答的重复和冲突
4. **质量评估** - 评估答案完整性和格式规范性
5. **数据录入** - 将确认后的数据写入Excel文件

#### 核心组件
- `agent.py` - 主协调器 (290行)
- `llm_clients.py` - LLM客户端封装，支持多平台分析功能
- `rule_based_recommender.py` - 基于规则的分类推荐器 (220行)
- `quality_checker.py` - 数据质量检测 (320行)
- `excel_writer.py` - Excel文件操作 (420行)

### 3. 多平台LLM实现 (`shared/llm_impl/`)

支持多个AI平台的统一接口实现：

#### 支持的平台
- **火山引擎** - `volcano_impl.py` - LLM和向量数据库
- **百炼平台** - `bailian_impl.py` - LLM服务
- **Google平台** - `google_impl.py` - LLM服务
- **重排序服务** - `rerank_impl.py` - 答案重排序
- **向量嵌入** - `embedding_impl.py` - 向量嵌入服务

### 4. FAQ管理系统 (`services/faq_management/`)

统一的FAQ数据管理服务：

#### 核心功能
- **数据仓库管理** - `faq_repo_manager.py` - 统一的FAQ数据加载和查询
- **多格式支持** - Excel和JSON格式的自动转换
- **多渠道支持** - 支持不同渠道的FAQ数据合并
- **自动备份** - 数据修改时的自动备份机制

### 5. API路由 (`server/routers/`)

RESTful API接口定义：

#### 主要接口
- `/api/v1/chat/faq_filter` - FAQ筛选对话接口
- `/api/v1/faq_recorder/analyze` - 问答分析接口
- `/api/v1/faq_recorder/categories` - 分类结构获取接口
- `/api/v1/faq_recorder/submit` - FAQ录入提交接口
- `/api/v1/hello` - 健康检查接口

## 环境配置

### 环境变量配置

复制环境变量模板文件：
```bash
cp .env.example .env
```

### 主要配置项

#### AI平台配置
```bash
# 百炼平台
BAILIAN_API_BASE=https://bailian.aliyuncs.com
BAILIAN_API_KEY=your_api_key
BAILIAN_MODEL=qwen-turbo

# 火山引擎
VOLCANO_API_BASE=https://ark.cn-beijing.volces.com
VOLCANO_API_KEY=your_api_key
VOLCANO_MODEL=doubao-pro-4k

# Coze平台
COZE_APP_BASE=https://api.coze.cn
COZE_API_KEY=your_api_key
COZE_WORKFLOW_ID=your_workflow_id

# Google平台
GOOGLE_API_BASE=https://generativelanguage.googleapis.com
GOOGLE_API_KEY=your_api_key
GOOGLE_MODEL=gemini-pro
```

#### 向量数据库配置
```bash
# 火山向量数据库
VOLCANO_KNOWLEDGE_API_BASE=https://api-knowledgebase.mlp.cn-beijing.volces.com
VOLCANO_KNOWLEDGE_API_AK=your_access_key
VOLCANO_KNOWLEDGE_API_SK=your_secret_key
VOLCANO_KNOWLEDGE_PROJECT=default
```

#### FAQ数据配置
```bash
# FAQ数据文件路径
FAQ_EXCEL_PATH=./resources/faq_data/faq.xlsx

# 提示词路径
REWRITE_PROMPT_PATH=./src/ai_app/agents/shared/prompts/common/rewrite_prompt.md
CLASSIFY_PROMPT_PATH=./src/ai_app/agents/shared/prompts/faq_filter/classify_prompt.md

# FAQ录入提示词
FAQ_ANALYSIS_SYSTEM_PROMPT_PATH=./src/ai_app/agents/shared/prompts/faq_recorder/analysis_system_prompt.md
FAQ_CATEGORY_RECOMMEND_SYSTEM_PROMPT_PATH=./src/ai_app/agents/shared/prompts/faq_recorder/category_recommend_system_prompt.md
```

#### 服务配置
```bash
# FastAPI配置
FASTAPI_HOST=0.0.0.0
FASTAPI_PORT=8000
APP_LOG_LEVEL=INFO
```

## 开发环境设置

### 1. 安装uv包管理器

```bash
# 参考官方文档安装uv
# https://docs.astral.sh/uv/getting-started/installation/
```

### 2. 创建虚拟环境

```bash
uv venv
```

### 3. 激活虚拟环境

```bash
# Linux/macOS
source .venv/bin/activate

# Windows CMD
.venv\Scripts\activate

# Windows PowerShell
.venv\Scripts\Activate.ps1
```

### 4. 安装依赖

```bash
uv sync
```

### 5. 启动开发服务器

```bash
# 基础启动
uv run python -m ai_app.server.main

# 启用详细日志
uv run python -m ai_app.server.main -v

# 启用热重载
uv run python -m ai_app.server.main --reload
```

## 测试

### FAQ筛选代理测试

```bash
# 测试查询重写
uv run python -m tests.agents.faq_filter_agent.test_llm_clients rewrite \
  --query "我密码忘了" --model-platform volcano

# 测试问题分类
uv run python -m tests.agents.faq_filter_agent.test_llm_clients classify \
  --query "我忘记密码了"

# 测试向量召回
uv run python -m tests.agents.faq_filter_agent.test_llm_clients retrieve \
  --query "我忘记密码了" --top-n 5
```

### FAQ录入代理测试

```bash
# 测试组件
python -m tests.agents.faq_recorder_agent.test_faq_recorder components -q 你好 -a 有何贵干

# 测试完整分析
python -m tests.agents.faq_recorder_agent.test_faq_recorder analyze -q 你好 -a 专员您好，您有什么问题要咨询？

# 测试提交
python -m tests.agents.faq_recorder_agent.test_faq_recorder submit -q "你好，用户忘记密码了怎么办？" -a "用户可以通过以下方式重置密码：1. 点击登录页面的'忘记密码'链接；2. 输入注册时的邮箱地址；3. 查收邮件并点击重置链接；4. 设置新密码。如果仍有问题，请联系客服。"
```

## 数据管理

### FAQ数据格式

#### Excel格式 (推荐)
Excel文件支持多sheet结构：
- **第一张表**: 通用数据表
- **后续表**: 各渠道增量数据表

每张表包含7列：
```
一级类别 | 二级类别 | 三级类别 | 四级类别 | 五级类别 | 答复 | 问题示例
```

#### JSON格式
```json
[
  {
    "category_desc": "账号",
    "sub_category": [
      {
        "category_desc": "密码找回",
        "sub_category": [...],
        "candidates": [
          {
            "answer": "专员，您好。请您联系客服，您可以通过客服电话或邮箱联系客服，客服会为您提供帮助。",
            "question_example": "我忘记密码了。",
            "key_path": "1.1.1"
          }
        ],
        "key_path": "1.1."
      },
      ...
    ],
    "candidates": [
      {
        "answer": "专员，您好。请问您能更具体的描述您的账号遇到的问题吗？",
        "question_example": "账号问题找谁问",
        "key_path": "1.2"
      }
    ],
    "key_path": "1."
  },
  ...
]
```

### 数据管理命令

```bash
# 查看FAQ数据统计
uv run python -c "from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager; repo = get_faq_repo_manager(True); print(repo.get_data_stats())"

# 验证数据完整性
uv run python -c "from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager; repo = get_faq_repo_manager(True); repo.create_parser().validate_data_structure()"

## API文档

### 启动服务后访问
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Schema**: http://localhost:8000/openapi.json

### 主要API端点

#### FAQ筛选接口
```bash
POST /api/v1/chat/faq_filter

请求体:
{
  "conversation": [
    {"role": "user", "content": "用户问题"},
    {"role": "assistant", "content": "AI回复"}
  ],
  "context": {
    "channel_name": "zulong",
    "platform": "volcano"
  }
}
```

#### FAQ分析接口
```bash
POST /api/v1/faq_recorder/analyze

请求体:
{
  "qa_pair": {
    "question": "用户问题",
    "answer": "标准答案"
  },
  "service": "volcano"
}
```

#### 分类结构请求接口
```bash
POST /api/v1/faq_recorder/categories

请求体:
{
  "channel": null,
  "max_depth": 3
}
```

#### 录入提交接口
```bash
POST /api/v1/faq_recorder/submit

请求体:
{
  "qa_pair": {
    "question": "用户问题",
    "answer": "标准答案"
  },
  "category_path": ["分类1", "分类2"]
}
```

## 性能优化

### 缓存策略
- **FAQ数据缓存** - 应用启动时加载到内存
- **LLM响应缓存** - 可配置的响应缓存
- **向量检索缓存** - 语义搜索结果缓存

### 异步处理
- **异步HTTP客户端** - 使用HTTPX进行异步请求
- **异步LLM调用** - 非阻塞的AI服务调用
- **异步数据库操作** - 高效的I/O操作

### 资源管理
- **连接池** - HTTP连接池管理
- **内存优化** - 大数据集的分块处理
- **垃圾回收** - 合理的对象生命周期管理

## 部署指南

### Docker部署

使用项目根目录的Docker配置：
```bash
cd ../docker
docker compose build backend_host
docker compose up backend_host
```

### 生产环境配置

1. **环境变量** - 配置生产环境的API密钥和地址
2. **日志级别** - 设置适当的日志级别（INFO/WARNING）
3. **资源限制** - 配置CPU和内存限制
4. **监控** - 集成监控和告警系统

### 负载均衡

- **多实例部署** - 支持多个后端实例
- **负载均衡** - 使用Nginx或云负载均衡器
- **自动扩缩容** - 基于负载的自动扩缩容

## 故障排除

### 常见问题

#### 1. 环境变量配置错误
```bash
# 检查环境变量
uv run python -c "from ai_app.config import config; print('API Key configured:', bool(config.volcano_api_key))"

# 验证配置
uv run python -c "from ai_app.config import config; config.check_volcano_vars()"
```

#### 2. FAQ数据文件问题
```bash
# 检查文件是否存在
uv run python -c "import os; from ai_app.config import config; print('FAQ file exists:', os.path.exists(config.faq_excel_path))"

# 验证数据格式
uv run python -c "from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager; repo = get_faq_repo_manager(True); repo.create_parser().validate_data_structure()"
```

### 调试技巧

#### 启用详细日志
```bash
uv run python -m ai_app.server.main -v
```

#### 检查请求追踪
```bash
# 查看请求ID
curl -X POST "http://localhost:8000/api/v1/chat/faq_filter" \
  -H "Content-Type: application/json" \
  -H "x-request-id: debug-request-123" \
  -d '{"conversation": [{"role": "user", "content": "测试"}],"context_params": {"channel_name": "zulong", "platform": "安卓"}, "service": "volcano"}'
```

#### 性能分析
```bash
# 使用性能分析工具
uv run python -m cProfile -o profile.stats -m ai_app.server.main
```

## 开发指南

### 添加新的AI平台

1. **创建实现类**
   ```python
   # src/ai_app/agents/shared/llm_impl/new_platform_impl.py
   from ai_app.agents.shared.llm_impl.base_llm_impl import BaseLLMImpl
   
   class NewPlatformLLMImpl(BaseLLMImpl):
       def __init__(self, api_key, api_base, model):
           # 初始化逻辑
           pass
   ```

2. **更新配置**
   ```python
   # src/ai_app/config.py
   @dataclass
   class ConfigManager:
       new_platform_api_key: str = os.getenv("NEW_PLATFORM_API_KEY")
       new_platform_api_base: str = os.getenv("NEW_PLATFORM_API_BASE")
       new_platform_model: str = os.getenv("NEW_PLATFORM_MODEL")
   ```

3. **集成到Agent**
   ```python
   # 在相应的agent.py中添加初始化逻辑
   ```

### 扩展FAQ数据源

1. **修改数据解析器**
   ```python
   # 按需修改src/ai_app/services/faq_management/data_parser.py
   ```

2. **更新仓库管理器**
   ```python
   # 按需修改src/ai_app/services/faq_management/faq_repo_manager.py
   ```

### 自定义工作流

1. **定义工作流逻辑**：TODO
2. **实现节点处理**：TODO

## 许可证

本项目采用内部许可证，详见LICENSE文件。