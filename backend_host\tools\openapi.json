{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/api/v1/chat/faq_filter": {"post": {"summary": "Chat To Faq Filter", "description": "处理到 FAQ Filter Agent 的聊天请求。", "operationId": "chat_to_faq_filter_api_v1_chat_faq_filter_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatToFaqFilterResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"ChatInputMessage": {"properties": {"role": {"type": "string", "title": "Role", "description": "消息发送者的角色（例如 'user', 'assistant'）"}, "content": {"type": "string", "title": "Content", "description": "消息内容"}}, "type": "object", "required": ["role", "content"], "title": "ChatInputMessage", "description": "聊天输入消息结构"}, "ChatModelUsage": {"properties": {"model_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model Id", "description": "被调用模型的 ID"}, "input_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Input Tokens", "description": "输入消耗的 token 数量"}, "output_tokens": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Output Tokens", "description": "输出消耗的 token 数量"}}, "type": "object", "title": "ChatModelUsage", "description": "单个模型调用的 token 使用情况"}, "ChatModelUsages": {"properties": {"models": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatModelUsage"}, "type": "array"}, {"type": "null"}], "title": "Models", "description": "包含所有模型调用使用情况的列表"}}, "type": "object", "title": "ChatModelUsages", "description": "多个模型调用的 token 使用情况汇总"}, "ChatRequest": {"properties": {"conversation": {"items": {"$ref": "#/components/schemas/ChatInputMessage"}, "type": "array", "title": "Conversation", "description": "包含历史消息的对话列表"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id", "description": "可选的会话 ID，用于保持对话上下文(暂时没用)"}, "service": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Service", "description": "可选的后端服务标识（例如 'volcano'（默认）, 'bailian'）"}, "context_params": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Context Params", "description": "可选的、传递给特定服务的额外上下文参数，比如channel、platform"}}, "type": "object", "required": ["conversation"], "title": "ChatRequest", "description": "发送给后端进行聊天的请求体"}, "ChatToFaqFilterCandidate": {"properties": {"content": {"type": "string", "title": "Content", "description": "根据分类结果返回的参考答案。分类失败时会包含一个<保底答案>"}, "category_chain": {"type": "string", "title": "Category Chain", "description": "问题分类链条，表示上述答案的分类路径。分类失败时字符串为空"}, "score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Score", "description": "此参考答案的置信度得分（如果可用，取值范围0~1）"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "AI归为此类的原因依据（如果可用）"}}, "type": "object", "required": ["content", "category_chain"], "title": "ChatToFaqFilterCandidate", "description": "单个候选回复的结构"}, "ChatToFaqFilterResponse": {"properties": {"response_code": {"type": "integer", "title": "Response Code", "description": "响应码（200为成功，其他均为失败）"}, "response_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Response Text", "description": "错误文本（如果有报错）"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id", "description": "本次交互使用的或新生成的会话 ID"}, "usages": {"anyOf": [{"$ref": "#/components/schemas/ChatModelUsages"}, {"type": "null"}], "description": "模型调用的 token 使用情况统计"}, "response_body": {"anyOf": [{"items": {"$ref": "#/components/schemas/ChatToFaqFilterCandidate"}, "type": "array"}, {"type": "null"}], "title": "Response Body", "description": "包含AI候选回复的列表，优先级从高到低"}, "rewritten_query": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rewritten Query", "description": "重写后的玩家问题说明"}, "classify_thinking": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Classify Thinking", "description": "分类思考过程（如果可用）"}}, "type": "object", "required": ["response_code"], "title": "ChatToFaqFilterResponse", "description": "后端返回给前端的聊天响应体"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}