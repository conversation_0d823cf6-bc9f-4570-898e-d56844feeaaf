[project]
name = "ai-app"
version = "0.1.0"
description = "Ai App based on FastAPI using LLM."
requires-python = ">=3.9"
authors = [
    { name = "yangkai", email = "<EMAIL>" }
]
dependencies = [
    "fastapi>=0.70.0",
    "httpx>=0.20.0",
    "jinja2>=3.0.0",
    "pandas[excel]>=1.3.0",
    "python-dotenv>=0.19.0",
    "requests>=2.26.0",
    "uvicorn[standard]>=0.15.0",
    "volcengine>=1.0.190",
    "fuzzywuzzy[speedup]>=0.18.0",
    "jieba>=0.42.1",
    "chromadb-client>=1.0.16",
    "openai>=1.99.9",
    "ollama>=0.5.3",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
