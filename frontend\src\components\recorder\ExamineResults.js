import React from 'react';

/**
 * 问答内容检查结果展示组件
 * @param {Object} props
 * @param {Object} props.examineResult - 检查结果数据
 */
function ExamineResults({ examineResult }) {
  if (!examineResult) {
    return null;
  }

  const {
    topic_domain,
    question_type,
    keywords,
    applicable_scenarios,
    category_suggestions,
    confidence,
    reasoning
  } = examineResult;

  // 根据置信度确定状态样式
  const getConfidenceStatus = (confidence) => {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.5) return 'medium';
    return 'low';
  };

  const confidenceStatus = getConfidenceStatus(confidence);

  return (
    <div className="examine-results">
      <div className="examine-header">
        <h3>🔍 内容分析结果</h3>
        <div className={`confidence-badge confidence-${confidenceStatus}`}>
          置信度: {(confidence * 100).toFixed(1)}%
        </div>
      </div>

      <div className="examine-content">
        {/* 基础分类信息 */}
        <div className="examine-section">
          <h4>📊 基础分类</h4>
          <div className="info-grid">
            <div className="info-item">
              <span className="info-label">主题领域:</span>
              <span className="info-value topic-domain">{topic_domain}</span>
            </div>
            <div className="info-item">
              <span className="info-label">问题类型:</span>
              <span className="info-value question-type">{question_type}</span>
            </div>
          </div>
        </div>

        {/* 关键词 */}
        {keywords && keywords.length > 0 && (
          <div className="examine-section">
            <h4>🏷️ 关键词</h4>
            <div className="keywords-list">
              {keywords.map((keyword, index) => (
                <span key={index} className="keyword-tag">
                  {keyword}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* 适用场景 */}
        {applicable_scenarios && applicable_scenarios.length > 0 && (
          <div className="examine-section">
            <h4>🎯 适用场景</h4>
            <ul className="scenarios-list">
              {applicable_scenarios.map((scenario, index) => (
                <li key={index} className="scenario-item">
                  {scenario}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 分类建议 */}
        {category_suggestions && category_suggestions.length > 0 && (
          <div className="examine-section">
            <h4>💡 分类建议</h4>
            <div className="suggestions-list">
              {category_suggestions.map((suggestion, index) => (
                <span key={index} className="suggestion-tag">
                  {suggestion}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* 分析推理 */}
        {reasoning && (
          <div className="examine-section">
            <h4>🤔 分析推理</h4>
            <div className="reasoning-content">
              <p>{reasoning}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default ExamineResults;