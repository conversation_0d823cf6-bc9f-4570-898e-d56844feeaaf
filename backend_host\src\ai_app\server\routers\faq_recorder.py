"""
FAQ录入代理API路由

提供FAQ录入相关的REST API接口。
"""

import json
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from ai_app.models.faq_recorder import (
    FAQRecorderAnalyzeRequest,
    FAQRecorderAnalyzeResponse,
    FAQRecorderCategoriesRequest,
    FAQRecorderCategoriesResponse,
    FAQRecorderSubmitRequest,
    FAQRecorderSubmitResponse
)
from ai_app.agents.faq_recorder_agent.agent import FAQRecorderAgent
from ai_app.shared.exceptions import FAQRecorderError, ConfigurationError
from ai_app.config import config
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

router = APIRouter(prefix="/faq_recorder", tags=["FAQ Recorder"])


@router.post("/analyze", response_model=FAQRecorderAnalyzeResponse)
async def analyze_qa_pair(request: FAQRecorderAnalyzeRequest):
    """
    分析问答对并推荐分类路径
    
    Args:
        request: 包含问答对和上下文参数的分析请求
        
    Returns:
        分析结果，包含分类推荐和质量检查
    """
    #logger.info("=========== /faq_recorder/analyze endpoint received request ==========")
    
    try:
        logger.debug(f"Request:\n{json.dumps(request.model_dump(), indent=2, ensure_ascii=False)}")
        
        # 验证和设置默认服务平台
        service = request.service or "volcano"
        if service not in ["volcano", "bailian", "google"]:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid service platform: {service}"
            )
        
        # 验证平台配置
        if service == "volcano":
            config.check_volcano_vars()
        elif service == "bailian":
            config.check_bailian_vars()
        elif service == "google":
            config.check_google_vars()
        
        # 初始化代理
        agent = FAQRecorderAgent(model_platform=service, channel=request.channel)
        
        # 执行分析
        response = await agent.analyze_qa_pair(request)
        
        logger.debug(f"Response:\n{json.dumps(response.model_dump(), indent=2, ensure_ascii=False)}")
        return response
        
    except HTTPException:
        raise
    except ConfigurationError as e:
        logger.error(f"Configuration error in analyze endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Configuration error: {str(e)}")
    except FAQRecorderError as e:
        logger.error(f"FAQ recorder error in analyze endpoint: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception("Unexpected error in analyze endpoint")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/categories", response_model=FAQRecorderCategoriesResponse)
def get_categories_structure(request: FAQRecorderCategoriesRequest):
    """
    获取FAQ分类结构
    
    Args:
        request: 包含渠道和深度参数的分类请求
        
    Returns:
        分类结构信息，包含树形结构和Markdown格式
    """
    #logger.info("=========== /faq_recorder/categories endpoint received request ==========")
    
    try:
        logger.debug(f"Request:\n{json.dumps(request.model_dump(), indent=2, ensure_ascii=False)}")
        
        # 初始化代理（分类结构获取不需要LLM，使用默认平台即可）
        agent = FAQRecorderAgent(model_platform="volcano", channel=request.channel)
        
        # 获取分类结构
        response = agent.get_categories_structure(request)
        
        logger.debug(f"Response code: {response.response_code}, categories: {response.total_categories}")
        return response
        
    except ConfigurationError as e:
        logger.error(f"Configuration error in categories endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Configuration error: {str(e)}")
    except FAQRecorderError as e:
        logger.error(f"FAQ recorder error in categories endpoint: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception("Unexpected error in categories endpoint")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/submit", response_model=FAQRecorderSubmitResponse)
async def submit_faq_entry(request: FAQRecorderSubmitRequest):
    """
    提交FAQ录入到Excel文件
    
    Args:
        request: 包含问答对、分类路径和提交选项的请求
        
    Returns:
        提交结果，包含写入信息和质量检查结果
    """
    #logger.info("=========== /faq_recorder/submit endpoint received request ==========")
    
    try:
        logger.debug(f"Request:\n{json.dumps(request.model_dump(), indent=2, ensure_ascii=False)}")
        
        # 验证分类路径
        if not request.category_path:
            raise HTTPException(
                status_code=400,
                detail="Category path cannot be empty"
            )
        
        if len(request.category_path) > 5:
            raise HTTPException(
                status_code=400,
                detail=f"Category path too deep: {len(request.category_path)} levels (max 5)"
            )
        
        # 初始化代理（提交不需要LLM，使用默认平台）
        agent = FAQRecorderAgent(model_platform="volcano", channel=request.channel)
        
        # 执行提交
        response = await agent.submit_faq_entry(request)
        
        logger.debug(f"Response code: {response.response_code}")
        return response
        
    except HTTPException:
        raise
    except ConfigurationError as e:
        logger.error(f"Configuration error in submit endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Configuration error: {str(e)}")
    except FAQRecorderError as e:
        logger.error(f"FAQ recorder error in submit endpoint: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception("Unexpected error in submit endpoint")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health")
def health_check():
    """
    健康检查接口
    
    Returns:
        服务状态信息
    """
    try:
        # 检查FAQ仓库管理器状态
        from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager
        
        repo_manager = get_faq_repo_manager()
        repo_stats = repo_manager.get_data_stats()
        
        return {
            "status": "healthy",
            "service": "faq_recorder",
            "version": "1.0.0",
            "faq_repo_status": {
                "loaded": repo_stats["loaded"],
                "total_categories": repo_stats["total_categories"],
                "available_channels": repo_stats["available_channels"]
            },
            "excel_path": config.faq_excel_path
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Service unhealthy: {str(e)}"
        )


@router.get("/status")
def get_service_status():
    """
    获取详细的服务状态信息
    
    Returns:
        详细的服务配置和状态信息
    """
    try:
        from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager
        
        repo_manager = get_faq_repo_manager()
        repo_stats = repo_manager.get_data_stats()
        
        # 获取Excel文件信息
        from ai_app.agents.faq_recorder_agent.excel_writer import FAQExcelWriter
        excel_writer = FAQExcelWriter(config.faq_excel_path)
        excel_info = excel_writer.get_excel_info()
        
        return {
            "service_info": {
                "name": "FAQ Recorder Agent",
                "version": "1.0.0",
                "description": "AI-powered FAQ recording and management service"
            },
            "configuration": {
                "excel_path": config.faq_excel_path,
                "supported_platforms": ["volcano", "bailian"],
                "max_category_levels": 5
            },
            "faq_repository": repo_stats,
            "excel_file": excel_info,
            "health": "ok"
        }
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return {
            "service_info": {
                "name": "FAQ Recorder Agent",
                "version": "1.0.0",
                "description": "AI-powered FAQ recording and management service"
            },
            "health": "error",
            "error": str(e)
        }


# 为了向后兼容，也可以添加一些简化的接口
@router.post("/quick_analyze")
async def quick_analyze(qa_data: Dict[str, Any]):
    """
    快速分析接口（简化版）
    
    Args:
        qa_data: 包含question和answer的字典
        
    Returns:
        简化的分析结果
    """
    try:
        from ai_app.models.faq_recorder import FAQRecorderQuestionAnswer, FAQRecorderAnalyzeRequest
        
        # 构建标准请求
        qa_pair = FAQRecorderQuestionAnswer(
            question=qa_data.get("question", ""),
            answer=qa_data.get("answer", ""),
            question_example=qa_data.get("question_example")
        )
        
        request = FAQRecorderAnalyzeRequest(
            qa_pair=qa_pair,
            channel=qa_data.get("channel"),
            service=qa_data.get("service", "volcano")
        )
        
        # 调用标准分析接口
        response = await analyze_qa_pair(request)
        
        # 返回简化结果
        return {
            "success": response.response_code == 200,
            "message": response.response_text,
            "recommendations": [
                {
                    "path": rec.category_path,
                    "confidence": rec.confidence,
                    "reason": rec.reason
                }
                for rec in response.recommended_categories[:3]  # 只返回前3个
            ],
            "quality_issues": {
                "warnings": response.quality_check.warnings if response.quality_check else [],
                "errors": response.quality_check.errors if response.quality_check else []
            }
        }
        
    except Exception as e:
        logger.error(f"Quick analyze failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))