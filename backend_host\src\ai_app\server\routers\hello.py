from fastapi import APIRouter
from ai_app.models.common import HelloResponse
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

router = APIRouter()

@router.get("/hello/{name}", response_model=HelloResponse)
async def say_hello(name: str):
    """接收路径参数 name 并返回问候消息。"""
    logger.info(f"Received /hello/{name} request")
    return {"message": f"Hello, {name}"}