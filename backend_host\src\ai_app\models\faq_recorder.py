"""
FAQ录入代理相关的数据模型
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from ai_app.models.chat import ChatResponse, ChatModelUsages

# === 输入数据模型 ===

class FAQRecorderQuestionAnswer(BaseModel):
    """问答对输入结构"""
    question: str = Field(..., description="用户输入的问题")
    answer: str = Field(..., description="对应的答案内容")
    question_example: Optional[str] = Field(None, description="问题示例（可选）")

class FAQRecorderAnalyzeRequest(BaseModel):
    """分析问答对的请求结构"""
    qa_pair: FAQRecorderQuestionAnswer = Field(..., description="待分析的问答对")
    channel: Optional[str] = Field(None, description="渠道名称，为空时使用通用数据")
    service: Optional[str] = Field("volcano", description="使用的LLM服务平台")

class FAQRecorderCategoriesRequest(BaseModel):
    """获取分类结构的请求"""
    channel: Optional[str] = Field(None, description="渠道名称，为空时返回通用分类")
    max_depth: Optional[int] = Field(-1, description="最大显示深度，-1表示无限制")

class FAQRecorderSubmitRequest(BaseModel):
    """提交录入数据的请求结构"""
    qa_pair: FAQRecorderQuestionAnswer = Field(..., description="问答对数据")
    category_path: List[str] = Field(..., description="选择的分类路径，如['账号', '密码相关', '找回密码']")
    channel: Optional[str] = Field(None, description="目标渠道")

# === 中间处理数据模型 ===

class CategoryRecommendation(BaseModel):
    """分类推荐结果"""
    category_path: List[str] = Field(..., description="推荐的分类路径")
    confidence: float = Field(..., description="推荐置信度，0-1之间")
    reason: str = Field(..., description="推荐理由")
    is_new_category: bool = Field(False, description="是否为新建分类建议")
    similar_existing: Optional[List[str]] = Field(None, description="相似的现有分类路径")

class QualityCheckResult(BaseModel):
    """质量检查结果"""
    is_valid: bool = Field(..., description="是否通过质量检查")
    warnings: List[str] = Field(default_factory=list, description="警告信息列表")
    errors: List[str] = Field(default_factory=list, description="错误信息列表") 
    duplicate_questions: List[Dict[str, Any]] = Field(default_factory=list, description="重复的问题信息")

class CategoryTreeNode(BaseModel):
    """分类树节点结构"""
    name: str = Field(..., description="分类名称")
    path: List[str] = Field(..., description="完整路径")
    level: int = Field(..., description="层级深度")
    children: List['CategoryTreeNode'] = Field(default_factory=list, description="子分类列表")
    has_answers: bool = Field(False, description="是否包含答案")
    answer_count: int = Field(0, description="答案数量")

# === 输出数据模型 ===

class FAQRecorderAnalyzeResponse(ChatResponse):
    """分析问答对的响应结构"""
    examine_result: Optional[Dict[str, Any]] = Field(None, description="问答对分析结果")
    recommended_categories: List[CategoryRecommendation] = Field(default_factory=list, description="推荐的分类路径列表")
    quality_check: Optional[QualityCheckResult] = Field(None, description="质量检查结果")
    usages: Optional[ChatModelUsages] = Field(None, description="模型调用统计")

class FAQRecorderCategoriesResponse(BaseModel):
    """获取分类结构的响应"""
    response_code: int = Field(..., description="响应码（200为成功）")
    response_text: Optional[str] = Field(None, description="错误文本（如果有报错）")
    category_tree: List[CategoryTreeNode] = Field(default_factory=list, description="分类树结构")
    markdown_structure: Optional[str] = Field(None, description="Markdown格式的分类结构")
    total_categories: int = Field(0, description="总分类数量")
    total_answers: int = Field(0, description="总答案数量")

class FAQRecorderSubmitResponse(BaseModel):
    """提交录入的响应结构"""
    response_code: int = Field(..., description="响应码（200为成功）")
    response_text: Optional[str] = Field(None, description="响应消息")
    submitted_data: Optional[Dict[str, Any]] = Field(None, description="已提交的数据信息")
    quality_check: Optional[QualityCheckResult] = Field(None, description="最终质量检查结果")
    excel_info: Optional[Dict[str, Any]] = Field(None, description="Excel文件操作信息")

# 解决循环引用问题
CategoryTreeNode.model_rebuild()