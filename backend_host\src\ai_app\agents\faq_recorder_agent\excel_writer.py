"""
Excel写入服务

负责将FAQ数据写入Excel文件，支持事务性操作和数据验证。
"""

import os
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

from ai_app.models.faq_recorder import FAQRecorderQuestionAnswer
from ai_app.shared.exceptions import ExcelWriteError, InvalidCategoryPathError
from ai_app.shared.request_tracing import get_traced_logger
from ai_app.shared.excel_utils import (
    DEFAULT_SHEET_NAME,
    MAX_BACKUPS,
    read_excel_file,
    write_dataframe_to_excel,
    create_empty_excel_file,
    create_dataframe_from_records,
    append_dataframe_rows,
    append_single_row,
    get_excel_file_info
)

logger = get_traced_logger(__name__)


class FAQExcelWriter:
    """
    FAQ Excel写入器
    
    功能：
    1. 将问答数据追加到Excel文件
    2. 验证分类路径的有效性
    3. 支持事务性操作（失败回滚）
    4. 自动创建备份
    """
    
    def __init__(self, excel_path: str):
        """
        初始化Excel写入器
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.backup_dir = os.path.join(os.path.dirname(excel_path), "backups")
        logger.debug(f"FAQExcelWriter initialized with path: {excel_path}")
    
    def write_faq_entry(
        self,
        qa_pair: FAQRecorderQuestionAnswer,
        category_path: List[str],
        sheet_name: str = None,
        create_backup: bool = True
    ) -> Dict[str, Any]:
        """
        写入FAQ条目到Excel文件
        
        Args:
            qa_pair: 问答对数据
            category_path: 分类路径，最多5级
            sheet_name: 工作表名称，默认为第一个工作表
            create_backup: 是否创建备份
            
        Returns:
            写入操作的结果信息
            
        Raises:
            ExcelWriteError: 写入操作失败
            InvalidCategoryPathError: 分类路径无效
        """
        try:
            logger.info(f"Starting to write FAQ entry to Excel: {qa_pair.question[:50]}...")
            
            # 1. 验证分类路径
            self._validate_category_path(category_path)
            
            # 2. 创建备份（如果需要）
            backup_path = None
            if create_backup:
                backup_path = self._create_backup()
                logger.debug(f"Created backup at: {backup_path}")
            
            try:
                # 4. 确定目标工作表
                target_sheet = self._determine_target_sheet(sheet_name)

                # 3. 读取现有Excel文件
                if not os.path.exists(self.excel_path):
                    # 如果文件不存在，创建一个新的
                    self._create_new_excel_file(target_sheet)
                
                # 5. 读取现有数据
                existing_data = read_excel_file(self.excel_path, sheet_name=target_sheet)
                
                # 6. 准备新行数据
                new_row = self._prepare_new_row(qa_pair, category_path)
                
                # 7. 追加新行
                updated_data = append_single_row(existing_data, new_row)
                
                # 8. 写入更新后的数据
                write_dataframe_to_excel(updated_data, self.excel_path, sheet_name=target_sheet)
                
                # 9. 返回成功结果
                result = {
                    "success": True,
                    "excel_path": self.excel_path,
                    "sheet_name": target_sheet,
                    "backup_path": backup_path,
                    "new_row_data": new_row,
                    "total_rows": len(updated_data),
                    "write_time": datetime.now().isoformat()
                }
                
                logger.info(f"Successfully wrote FAQ entry to Excel, total rows: {result['total_rows']}")
                return result
                
            except Exception as e:
                # 如果写入失败且有备份，尝试恢复
                if backup_path and os.path.exists(backup_path):
                    logger.warning(f"Write operation failed, attempting to restore backup: {e}")
                    self._restore_backup(backup_path)
                raise ExcelWriteError(f"Failed to write FAQ entry: {e}") from e
                
        except (ExcelWriteError, InvalidCategoryPathError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during Excel write operation: {e}")
            raise ExcelWriteError(f"Unexpected error: {e}") from e
    
    def batch_write_faq_entries(
        self,
        entries: List[Tuple[FAQRecorderQuestionAnswer, List[str]]],
        sheet_name: str = None,
        create_backup: bool = True
    ) -> Dict[str, Any]:
        """
        批量写入FAQ条目
        
        Args:
            entries: (问答对, 分类路径) 的元组列表
            sheet_name: 工作表名称
            create_backup: 是否创建备份
            
        Returns:
            批量写入操作的结果信息
            
        Raises:
            ExcelWriteError: 写入操作失败
        """
        try:
            logger.info(f"Starting batch write of {len(entries)} FAQ entries")
            
            # 验证所有条目
            for i, (qa_pair, category_path) in enumerate(entries):
                try:
                    self._validate_category_path(category_path)
                except InvalidCategoryPathError as e:
                    raise ExcelWriteError(f"Invalid category path in entry {i+1}: {e}") from e
            
            # 创建备份
            backup_path = None
            if create_backup:
                backup_path = self._create_backup()
            
            try:
                target_sheet = self._determine_target_sheet(sheet_name)

                # 读取现有数据
                if not os.path.exists(self.excel_path):
                    self._create_new_excel_file(target_sheet)
                
                existing_data = read_excel_file(self.excel_path, sheet_name=target_sheet)
                
                # 准备所有新行
                new_rows = []
                for qa_pair, category_path in entries:
                    new_row = self._prepare_new_row(qa_pair, category_path)
                    new_rows.append(new_row)
                
                # 批量追加
                updated_data = append_dataframe_rows(existing_data, new_rows)
                
                # 写入Excel
                write_dataframe_to_excel(updated_data, self.excel_path, sheet_name=target_sheet)
                
                result = {
                    "success": True,
                    "excel_path": self.excel_path,
                    "sheet_name": target_sheet,
                    "backup_path": backup_path,
                    "entries_written": len(entries),
                    "total_rows": len(updated_data),
                    "write_time": datetime.now().isoformat()
                }
                
                logger.info(f"Successfully wrote {len(entries)} FAQ entries to Excel")
                return result
                
            except Exception as e:
                if backup_path and os.path.exists(backup_path):
                    logger.warning("Batch write failed, restoring backup")
                    self._restore_backup(backup_path)
                raise ExcelWriteError(f"Batch write failed: {e}") from e
                
        except ExcelWriteError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error during batch write: {e}")
            raise ExcelWriteError(f"Unexpected error: {e}") from e
    
    def _validate_category_path(self, category_path: List[str]) -> None:
        """
        验证分类路径的有效性
        
        Args:
            category_path: 分类路径
            
        Raises:
            InvalidCategoryPathError: 分类路径无效
        """
        if not category_path:
            raise InvalidCategoryPathError("Category path cannot be empty")
        
        if len(category_path) > 5:
            raise InvalidCategoryPathError(f"Category path too long: {len(category_path)} levels (max 5)")
        
        for i, category in enumerate(category_path):
            if not isinstance(category, str):
                raise InvalidCategoryPathError(f"Category at level {i+1} must be string, got {type(category)}")
            
            if not category.strip():
                raise InvalidCategoryPathError(f"Category at level {i+1} cannot be empty")
            
            if len(category) > 100:
                raise InvalidCategoryPathError(f"Category at level {i+1} too long: {len(category)} chars (max 100)")
    
    def _create_backup(self) -> str:
        """
        创建Excel文件备份
        
        Returns:
            备份文件路径
        """
        if not os.path.exists(self.excel_path):
            logger.debug("Original Excel file does not exist, skipping backup")
            return None
        
        # 创建备份目录
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.basename(self.excel_path)
        name, ext = os.path.splitext(filename)
        backup_filename = f"{name}_backup_{timestamp}{ext}"
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        # 复制文件
        shutil.copy2(self.excel_path, backup_path)
        logger.debug(f"Created backup: {backup_path}")
        
        # 清理旧备份（保留最近10个）
        self._cleanup_old_backups()
        
        return backup_path
    
    def _restore_backup(self, backup_path: str) -> None:
        """
        从备份恢复Excel文件
        
        Args:
            backup_path: 备份文件路径
        """
        try:
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self.excel_path)
                logger.info(f"Restored Excel file from backup: {backup_path}")
            else:
                logger.warning(f"Backup file not found: {backup_path}")
        except Exception as e:
            logger.error(f"Failed to restore backup: {e}")
    
    def _cleanup_old_backups(self, max_backups: int = MAX_BACKUPS) -> None:
        """
        清理旧的备份文件
        
        Args:
            max_backups: 保留的最大备份数量
        """
        try:
            if not os.path.exists(self.backup_dir):
                return
            
            # 获取所有备份文件
            backup_files = []
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.xlsx') and '_backup_' in filename:
                    filepath = os.path.join(self.backup_dir, filename)
                    backup_files.append((filepath, os.path.getctime(filepath)))
            
            # 按创建时间排序
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除多余的备份
            for filepath, _ in backup_files[max_backups:]:
                try:
                    os.remove(filepath)
                    logger.debug(f"Removed old backup: {filepath}")
                except Exception as e:
                    logger.warning(f"Failed to remove old backup {filepath}: {e}")
                    
        except Exception as e:
            logger.warning(f"Error during backup cleanup: {e}")
    
    def _create_new_excel_file(self, sheet_name: str) -> None:
        """
        创建新的Excel文件
        
        Args:
            sheet_name: 工作表名称
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(self.excel_path), exist_ok=True)
        
        # 使用工具函数创建新文件
        create_empty_excel_file(self.excel_path, sheet_name)
        
        logger.info(f"Created new Excel file: {self.excel_path}")
    
    def _determine_target_sheet(self, sheet_name: Optional[str]) -> str:
        """
        确定目标工作表名称
        
        Args:
            sheet_name: 指定的工作表名称
            
        Returns:
            实际使用的工作表名称
        """
        if sheet_name and sheet_name.strip():
            return sheet_name
        
        # 使用工具函数获取第一个工作表名称
        return DEFAULT_SHEET_NAME
    
    def _prepare_new_row(
        self, 
        qa_pair: FAQRecorderQuestionAnswer, 
        category_path: List[str]
    ) -> Dict[str, str]:
        """
        准备新行数据
        
        Args:
            qa_pair: 问答对
            category_path: 分类路径
            
        Returns:
            新行数据字典
        """
        # 标准的7列结构
        row_data = {
            "一级类别": "",
            "二级类别": "",
            "三级类别": "",
            "四级类别": "",
            "五级类别": "",
            "答复": qa_pair.answer,
            "问题示例": qa_pair.question_example or qa_pair.question
        }
        
        # 填入分类路径
        category_columns = ["一级类别", "二级类别", "三级类别", "四级类别", "五级类别"]
        for i, category in enumerate(category_path):
            if i < len(category_columns):
                row_data[category_columns[i]] = category
        
        return row_data
    
    def get_excel_info(self) -> Dict[str, Any]:
        """
        获取Excel文件信息
        
        Returns:
            Excel文件信息字典
        """
        # 使用工具函数获取Excel文件信息
        info = get_excel_file_info(self.excel_path)
        
        # 添加备份目录信息
        if info.get("exists", False):
            info["backup_dir"] = self.backup_dir
        
        return info