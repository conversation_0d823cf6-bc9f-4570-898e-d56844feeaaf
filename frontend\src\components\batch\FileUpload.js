import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { parseExcelFile } from '../../utils/fileParser';

function FileUpload({ onFileParsed }) {
  const [fileName, setFileName] = useState('');
  const [error, setError] = useState('');

  const onDrop = useCallback(async (acceptedFiles) => {
    setError('');
    setFileName('');
    const file = acceptedFiles[0];
    if (!file) {
      return;
    }

    // 文件类型前端校验
    if (!file.name.endsWith('.xlsx')) {
        setError('文件格式不正确，请上传 .xlsx 文件。');
        return;
    }

    setFileName(file.name);

    const reader = new FileReader();
    reader.onabort = () => setError('文件读取被中断');
    reader.onerror = () => setError('文件读取失败');
    reader.onload = async () => {
      try {
        const binaryStr = reader.result;
        const parsedData = await parseExcelFile(binaryStr);
        onFileParsed(parsedData);
      } catch (err) {
        setError(`文件解析错误: ${err.message}`);
        onFileParsed([]); // 解析失败时清空数据
      }
    };
    reader.readAsArrayBuffer(file);
  }, [onFileParsed]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop });

  return (
    <div className="file-upload-container">
      <h4>1. 上传测试文件 (.xlsx)</h4>
      <div {...getRootProps()} className={`dropzone ${isDragActive ? 'active' : ''}`}>
        <input {...getInputProps()} />
        {
          isDragActive ?
            <p>将文件拖到这里...</p> :
            <p>将 .xlsx 文件拖到此处，或点击选择文件</p>
        }
      </div>
      {fileName && <p className="file-name">已选择文件: {fileName}</p>}
      {error && <p className="error-message">{error}</p>}
    </div>
  );
}

export default FileUpload;
