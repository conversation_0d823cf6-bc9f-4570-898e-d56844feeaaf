/**
 * CategorySelector 组件的工具函数集合
 * 将复杂的逻辑函数抽离出来，避免组件内部的循环依赖
 */

/**
 * 生成节点唯一ID
 * @param {Array} path - 节点路径
 * @returns {string} 节点ID
 */
export const generateNodeId = (path) => path.join('|');

/**
 * 生成UUID用于虚拟节点
 * @returns {string} UUID
 */
export const generateUUID = () => {
  return 'virtual_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * 将原始分类树转换为统一的节点结构
 * @param {Array} nodes - 原始节点数组
 * @param {Array} parentPath - 父节点路径
 * @returns {Array} 转换后的节点数组
 */
export const convertToUnifiedTree = (nodes, parentPath = []) => {
  return nodes.map(node => ({
    id: generateNodeId(node.path),
    name: node.name,
    path: node.path,
    children: node.children ? convertToUnifiedTree(node.children, node.path) : [],
    answer_count: node.answer_count || 0,
    isVirtual: false,
    parentId: parentPath.length > 0 ? generateNodeId(parentPath) : null
  }));
};

/**
 * 在树中查找节点（通过ID）
 * @param {Array} tree - 树结构
 * @param {string} nodeId - 节点ID
 * @returns {Object|null} 找到的节点或null
 */
export const findNodeInTree = (tree, nodeId) => {
  for (const node of tree) {
    if (node.id === nodeId) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeInTree(node.children, nodeId);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 在树中通过路径查找节点
 * @param {Array} tree - 树结构
 * @param {Array} targetPath - 目标路径
 * @returns {Object|null} 找到的节点或null
 */
export const findNodeByPath = (tree, targetPath) => {
  for (const node of tree) {
    if (JSON.stringify(node.path) === JSON.stringify(targetPath)) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeByPath(node.children, targetPath);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 在树中添加节点
 * @param {Array} tree - 树结构
 * @param {Object} newNode - 新节点
 * @param {Array} parentPath - 父节点路径
 * @returns {Array} 更新后的树结构
 */
export const addNodeToTree = (tree, newNode, parentPath) => {
  if (parentPath.length === 0) {
    // 添加到根级
    return [...tree, newNode];
  }
  
  return tree.map(node => {
    if (JSON.stringify(node.path) === JSON.stringify(parentPath)) {
      return {
        ...node,
        children: [...(node.children || []), newNode]
      };
    }
    if (node.children && node.children.length > 0) {
      return {
        ...node,
        children: addNodeToTree(node.children, newNode, parentPath)
      };
    }
    return node;
  });
};

/**
 * 从树中删除节点
 * @param {Array} tree - 树结构
 * @param {string} nodeId - 要删除的节点ID
 * @returns {Array} 更新后的树结构
 */
export const removeNodeFromTree = (tree, nodeId) => {
  return tree.filter(node => {
    if (node.id === nodeId) {
      return false;
    }
    if (node.children && node.children.length > 0) {
      node.children = removeNodeFromTree(node.children, nodeId);
    }
    return true;
  });
};

/**
 * 更新树中的节点
 * @param {Array} tree - 树结构
 * @param {string} nodeId - 要更新的节点ID
 * @param {Object} updates - 更新内容
 * @returns {Array} 更新后的树结构
 */
export const updateNodeInTree = (tree, nodeId, updates) => {
  return tree.map(node => {
    if (node.id === nodeId) {
      return { ...node, ...updates };
    }
    if (node.children && node.children.length > 0) {
      return {
        ...node,
        children: updateNodeInTree(node.children, nodeId, updates)
      };
    }
    return node;
  });
};

/**
 * 创建AI推荐的虚拟节点
 * @param {Array} recommendations - AI推荐数据
 * @param {Array} categoryTree - 原始分类树
 * @returns {Array} AI虚拟节点数组
 */
export const createAiVirtualNodes = (recommendations, categoryTree) => {
  const virtualNodes = [];
  const realTree = categoryTree && categoryTree.length > 0 ? convertToUnifiedTree(categoryTree) : [];
  
  if (!recommendations || !Array.isArray(recommendations)) {
    return virtualNodes;
  }
  
  recommendations.forEach(rec => {
    if (rec.category_path && rec.category_path.length > 0) {
      // 检查路径是否已存在于真实分类树中
      const existsInRealTree = findNodeByPath(realTree, rec.category_path);
      
      if (!existsInRealTree) {
        // 创建完整的路径节点链
        for (let i = 1; i <= rec.category_path.length; i++) {
          const currentPath = rec.category_path.slice(0, i);
          const parentPath = currentPath.slice(0, -1);
          
          // 检查当前路径是否已经在虚拟节点中
          const existsInVirtual = virtualNodes.some(vNode => 
            JSON.stringify(vNode.path) === JSON.stringify(currentPath)
          );
          
          // 检查当前路径是否已经在真实树中
          const existsInReal = findNodeByPath(realTree, currentPath);
          
          if (!existsInVirtual && !existsInReal) {
            const nodeId = 'ai_virtual_' + currentPath.join('|');
            virtualNodes.push({
              id: nodeId,
              name: currentPath[currentPath.length - 1],
              path: currentPath,
              children: [],
              answer_count: 0,
              isVirtual: true,
              isAiRecommended: true,
              parentId: parentPath.length > 0 ? 'ai_virtual_' + parentPath.join('|') : null
            });
          }
        }
      }
    }
  });
  
  return virtualNodes;
};

/**
 * 合并真实树和AI虚拟节点
 * @param {Array} realTree - 真实树结构
 * @param {Array} aiVirtualNodes - AI虚拟节点数组
 * @returns {Array} 合并后的树结构
 */
export const mergeTreeWithAiVirtualNodes = (realTree, aiVirtualNodes) => {
  let mergedTree = [...realTree];
  
  // 按路径长度排序，确保父节点先于子节点处理
  const sortedVirtualNodes = [...aiVirtualNodes].sort((a, b) => a.path.length - b.path.length);
  
  sortedVirtualNodes.forEach(virtualNode => {
    const parentPath = virtualNode.path.slice(0, -1);
    
    if (parentPath.length === 0) {
      // 根级虚拟节点
      mergedTree.push(virtualNode);
    } else {
      // 查找父节点并添加为子节点
      mergedTree = addNodeToTree(mergedTree, virtualNode, parentPath);
    }
  });
  
  return mergedTree;
};

/**
 * 检查路径是否被AI推荐
 * @param {Array} path - 节点路径
 * @param {Array} recommendations - AI推荐数据
 * @returns {boolean} 是否被推荐
 */
export const isRecommendedPath = (path, recommendations) => {
  if (!recommendations || !Array.isArray(recommendations)) {
    return false;
  }
  return recommendations.some(rec => 
    JSON.stringify(rec.category_path) === JSON.stringify(path)
  );
};

/**
 * 获取推荐信息
 * @param {Array} path - 节点路径
 * @param {Array} recommendations - AI推荐数据
 * @returns {Object|undefined} 推荐信息
 */
export const getRecommendationInfo = (path, recommendations) => {
  if (!recommendations || !Array.isArray(recommendations)) {
    return undefined;
  }
  return recommendations.find(rec => 
    JSON.stringify(rec.category_path) === JSON.stringify(path)
  );
};

/**
 * 检查同级节点是否存在同名
 * @param {Array} unifiedTree - 统一树结构
 * @param {Array} parentPath - 父节点路径
 * @param {string} newName - 新名称
 * @param {string} excludeNodeId - 排除的节点ID
 * @returns {boolean} 是否存在冲突
 */
export const checkSiblingNameConflict = (unifiedTree, parentPath, newName, excludeNodeId = null) => {
  // 获取同级节点
  let siblings = [];
  
  if (parentPath.length === 0) {
    // 根级节点
    siblings = unifiedTree;
  } else {
    // 查找父节点
    const parentNode = findNodeByPath(unifiedTree, parentPath);
    if (parentNode && parentNode.children) {
      siblings = parentNode.children;
    }
  }
  
  // 检查是否有同名的兄弟节点（排除自己）
  return siblings.some(sibling => 
    sibling.name.toLowerCase() === newName.toLowerCase() && 
    sibling.id !== excludeNodeId
  );
};

/**
 * 生成唯一的节点名称
 * @param {Array} unifiedTree - 统一树结构
 * @param {Array} parentPath - 父节点路径
 * @param {string} baseName - 基础名称
 * @returns {string} 唯一名称
 */
export const generateUniqueName = (unifiedTree, parentPath, baseName) => {
  let counter = 1;
  let uniqueName = baseName;
  
  while (checkSiblingNameConflict(unifiedTree, parentPath, uniqueName)) {
    uniqueName = `${baseName}${counter}`;
    counter++;
  }
  
  return uniqueName;
};

/**
 * 收集所有节点ID的递归函数
 * @param {Array} nodes - 节点数组
 * @returns {Array} 所有节点ID数组
 */
export const collectAllNodeIds = (nodes) => {
  const nodeIds = [];
  
  const traverse = (nodeList) => {
    nodeList.forEach(node => {
      if (node.children && node.children.length > 0) {
        nodeIds.push(node.id);
        traverse(node.children);
      }
    });
  };
  
  traverse(nodes);
  return nodeIds;
};

/**
 * 提取树中手动创建的虚拟节点（非AI推荐的虚拟节点）
 * @param {Array} tree - 树结构
 * @returns {Array} 手动虚拟节点数组
 */
export const extractManualVirtualNodes = (tree) => {
  const manualNodes = [];
  const traverse = (nodes) => {
    nodes.forEach(node => {
      if (node.isVirtual && !node.isAiRecommended) {
        manualNodes.push(node);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(tree);
  return manualNodes;
};

/**
 * 合并基础树和手动虚拟节点
 * @param {Array} baseTree - 基础树结构（真实节点 + AI虚拟节点）
 * @param {Array} manualVirtualNodes - 手动创建的虚拟节点数组
 * @returns {Array} 合并后的树结构
 */
export const mergeTreeWithManualVirtualNodes = (baseTree, manualVirtualNodes) => {
  if (!manualVirtualNodes || manualVirtualNodes.length === 0) {
    return baseTree;
  }
  
  let mergedTree = [...baseTree];
  manualVirtualNodes.forEach(virtualNode => {
    const parentPath = virtualNode.path.slice(0, -1);
    mergedTree = addNodeToTree(mergedTree, virtualNode, parentPath);
  });
  
  return mergedTree;
};

/**
 * 获取过滤后的树（用于搜索）
 * @param {Array} unifiedTree - 统一树结构
 * @param {string} searchTerm - 搜索词
 * @param {Function} setExpandedNodes - 设置展开节点的函数
 * @returns {Array} 过滤后的树结构
 */
export const getFilteredTree = (unifiedTree, searchTerm, setExpandedNodes) => {
  if (!searchTerm.trim()) {
    return unifiedTree;
  }

  const filterTree = (nodes) => {
    return nodes.reduce((filtered, node) => {
      const matchesSearch = node.name.toLowerCase().includes(searchTerm.toLowerCase());
      const filteredChildren = node.children ? filterTree(node.children) : [];
      
      if (matchesSearch || filteredChildren.length > 0) {
        filtered.push({
          ...node,
          children: filteredChildren
        });
        
        // 自动展开匹配的节点
        if (matchesSearch) {
          setExpandedNodes(prev => new Set([...prev, node.id]));
        }
      }
      
      return filtered;
    }, []);
  };

  return filterTree(unifiedTree);
};