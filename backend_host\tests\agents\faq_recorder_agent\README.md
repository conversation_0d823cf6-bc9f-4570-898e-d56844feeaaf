# FAQ录入代理测试工具

这个目录包含了FAQ录入代理的测试工具和测试数据。

## 测试文件说明

- `test_faq_recorder.py` - 主要的测试脚本，提供命令行接口
- `test_data_examples.json` - 测试数据示例
- `README.md` - 本说明文档

## 使用方法

### 前提条件

1. 确保已正确配置环境变量（在 `.env` 文件中）
2. 确保FAQ Excel文件存在且可访问
3. 确保相关AI平台的API密钥配置正确

### 运行测试

在项目根目录下执行以下命令：

```bash
cd backend_host

# 运行所有测试
python -m tests.agents.faq_recorder_agent.test_faq_recorder all

# 运行特定测试
python -m tests.agents.faq_recorder_agent.test_faq_recorder analyze
python -m tests.agents.faq_recorder_agent.test_faq_recorder categories  
python -m tests.agents.faq_recorder_agent.test_faq_recorder submit
python -m tests.agents.faq_recorder_agent.test_faq_recorder components

# 指定LLM平台
python -m tests.agents.faq_recorder_agent.test_faq_recorder all --platform volcano
python -m tests.agents.faq_recorder_agent.test_faq_recorder all --platform bailian

# 使用自定义问答测试
python -m tests.agents.faq_recorder_agent.test_faq_recorder analyze \
  --question "用户如何修改密码？" \
  --answer "请进入设置页面，选择安全设置，然后点击修改密码。"

# 使用自定义分类路径测试提交
python -m tests.agents.faq_recorder_agent.test_faq_recorder submit \
  --question "如何联系客服？" \
  --answer "可以通过电话400-123-4567联系客服。" \
  --category "客服支持" "联系方式"
```

### 测试类型说明

1. **all** - 运行所有测试，包括：
   - 独立组件测试
   - 分类结构获取测试
   - 问答分析测试
   - 录入提交测试

2. **analyze** - 测试问答对分析功能：
   - LLM内容分析
   - 分类推荐（LLM + 规则）
   - 质量检查

3. **categories** - 测试分类结构获取功能：
   - 获取完整分类树
   - 统计分类和答案数量
   - Markdown格式输出

4. **submit** - 测试FAQ录入提交功能：
   - 质量检查
   - Excel文件写入
   - 备份机制

5. **components** - 测试各个独立组件：
   - 分类推荐器
   - 质量检测器
   - Excel写入器

### 预期输出

测试成功时会显示：
```
=== 测试问答对分析功能 (平台: volcano) ===
正在分析问答对...
分析结果:
  响应码: 200
  响应消息: 分析完成
  LLM分析:
    主题领域: 账号管理
    问题类型: 操作指导
    置信度: 0.85
  分类推荐数量: 5
    推荐1: 账号管理 > 密码相关 > 找回密码 (置信度: 0.90)
           理由: 关键词匹配: 密码, 忘记
  质量检查:
    通过: True
    警告数: 1
    错误数: 0
分析测试完成 ✓
```

### 故障排除

1. **配置错误**：检查 `.env` 文件中的API密钥配置
2. **FAQ文件不存在**：确保 `FAQ_EXCEL_PATH` 指向的Excel文件存在
3. **网络连接问题**：检查AI平台API的网络连接
4. **权限问题**：确保有Excel文件的读写权限

### API测试

也可以直接测试API接口：

```bash
# 启动服务器
uv run python -m ai_app.server.main

# 使用curl测试分析接口
curl -X POST "http://localhost:8000/api/v1/faq_recorder/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "qa_pair": {
      "question": "用户忘记密码了怎么办？",
      "answer": "可以通过邮箱重置密码。"
    },
    "service": "volcano"
  }'

# 测试分类结构接口
curl -X POST "http://localhost:8000/api/v1/faq_recorder/categories" \
  -H "Content-Type: application/json" \
  -d '{
    "channel": null,
    "max_depth": 3
  }'

# 测试提交接口
curl -X POST "http://localhost:8000/api/v1/faq_recorder/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "qa_pair": {
      "question": "如何充值？",
      "answer": "请进入充值页面选择支付方式。"
    },
    "category_path": ["支付相关", "充值问题"]
  }'
```

## 测试数据

`test_data_examples.json` 文件包含了：

- **test_qa_pairs** - 5个典型的问答对测试用例
- **test_categories** - 常见的分类路径示例
- **quality_test_cases** - 质量检查的测试用例

可以根据实际需求修改这些测试数据。

## 注意事项

1. 测试会调用真实的AI平台API，可能产生费用
2. 提交测试会实际写入Excel文件，建议先备份
3. 某些测试需要网络连接和有效的API密钥
4. 测试结果可能因AI模型响应而有所不同