"""Custom exceptions for the AI Agent module."""

class AgentError(Exception):
    """Base exception for errors raised by the agent module."""
    pass

class ConfigError(AgentError):
    """Exception raised for errors in configuration."""
    pass

class FAQDataError(AgentError):
    """Exception raised for errors related to FAQ data loading or parsing."""
    pass

class LLMAPIError(AgentError):
    """Exception raised for errors during LLM API calls."""
    pass

class LLMResponseError(LLMAPIError):
    """Exception raised for errors in the LLM API response (e.g., format)."""
    pass 

class PromptLoadError(AgentError):
    """Exception raised when prompt loading fails."""
    pass

class ConfigurationError(AgentError):
    """Exception raised when configuration fails."""
    pass

class EmbeddingCollectionNotFoundError(AgentError):
    """Exception raised when embedding collection is not found."""
    pass

# FAQ管理相关异常
class FAQRepoError(AgentError):
    """Exception raised for errors in FAQ repository operations."""
    pass

class FAQRepoNotFoundError(FAQRepoError):
    """Exception raised when FAQ repository is not found."""
    pass

class ExcelLoadError(FAQRepoError):
    """Exception raised when Excel file loading fails."""
    pass

class ExcelWriteError(FAQRepoError):
    """Exception raised when Excel file writing fails."""
    pass

# FAQ录入相关异常
class FAQRecorderError(AgentError):
    """Exception raised for errors in FAQ recorder operations."""
    pass

class ExamineQAPairError(FAQRecorderError):
    """Exception raised when examination fails."""
    pass

class CategoryRecommendationError(FAQRecorderError):
    """Exception raised when category recommendation fails."""
    pass

class QualityCheckError(FAQRecorderError):
    """Exception raised when quality check fails."""
    pass

class DuplicateQuestionError(QualityCheckError):
    """Exception raised when duplicate questions are found."""
    pass

class CategoryConflictError(QualityCheckError):
    """Exception raised when category conflicts are detected."""
    pass

class InvalidCategoryPathError(FAQRecorderError):
    """Exception raised when category path is invalid."""
    pass
