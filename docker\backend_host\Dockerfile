# ========= Stage 1: Dependencies =========
FROM python:3.11-slim AS builder

# 配置清华源并安装编译依赖和 uv
RUN sed -i 's|http://deb.debian.org|https://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list.d/debian.sources \
    && apt-get update && apt-get install -y gcc g++ make \
    && pip install -i https://pypi.tuna.tsinghua.edu.cn/simple uv \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖管理文件
COPY ./pyproject.toml ./uv.lock ./.python-version ./

# 安装依赖并创建虚拟环境
# 这一步会被缓存，只要 uv.lock 没有变化
# 使用 --no-install-project 参数来避免安装项目本身
# 配置 uv 使用清华源
RUN uv sync --frozen --no-cache --no-install-project --index-url https://pypi.tuna.tsinghua.edu.cn/simple

# ========= Stage 2: Final Image =========
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 从 builder 阶段复制已经安装好依赖的虚拟环境
COPY --from=builder /app/.venv ./.venv

# 复制项目源代码
COPY ./src ./src
COPY ./resources ./resources

# 暴露 FastAPI 应用运行的端口
EXPOSE 8000

# 设置 PYTHONPATH
ENV PYTHONPATH=/app/src

# 指定启动命令
# 使用 /app/.venv 中的 python 解释器来运行 src 包下的 app 模块
CMD [".venv/bin/python", "-m", "ai_app.server.main", "--host", "0.0.0.0", "--port", "8000"] 