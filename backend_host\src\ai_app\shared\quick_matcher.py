#!/usr/bin/env python3
"""
基于fuzzywuzzy的快速文本相似度匹配工具

提供文本相似度计算和去重功能，支持多种匹配算法。
"""

from typing import List, Tuple, Dict, Any, Optional
from fuzzywuzzy import fuzz, process
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)


class QuickMatcher:
    """基于fuzzywuzzy的快速文本相似度匹配器
    
    提供多种文本相似度计算方法和去重功能。
    """
    
    def __init__(self, similarity_threshold: float = 0.8):
        """
        初始化匹配器
        
        Args:
            similarity_threshold: 相似度阈值，用于判断文本是否重复 (0.0-1.0)
        """
        if not 0.0 <= similarity_threshold <= 1.0:
            raise ValueError("similarity_threshold must be between 0.0 and 1.0")
        
        self.similarity_threshold = similarity_threshold

    @staticmethod
    def get_scorers_meta() -> Tuple[Dict[str, Any], str]:
        """
        获取所有相似度计算方法的评分器元数据和默认方法
        """
        scorers_list = [
            ("WRatio", fuzz.WRatio),
            ("ratio", fuzz.ratio),
            ("partial_ratio", fuzz.partial_ratio),
            ("token_sort_ratio", fuzz.token_sort_ratio),
            ("token_set_ratio", fuzz.token_set_ratio),
        ]
        scorers_table = {name: scorer for name, scorer in scorers_list}
        return scorers_table, scorers_list[0][0]
    
    def _get_scorer(self, method: Optional[str] = None):
        """
        获取相似度计算方法的评分器
        
        Args:
            method: 相似度计算方法名称
        
        Returns:
            对应的fuzz评分器函数
        
        Raises:
            ValueError: 当method参数无效时
        """
        scorers, default_method = self.get_scorers_meta()
        method = method or default_method
        
        if method not in scorers:
            valid_methods = ", ".join(scorers.keys())
            raise ValueError(f"Invalid method: {method}. Must be one of: {valid_methods}")
        
        return scorers[method]
    
    def calculate_similarity(self, text1: str, text2: str, method: Optional[str] = None) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            method: 相似度计算方法，可选值：
                   - "WRatio": 智能加权相似度 (默认，推荐)
                   - "ratio": 基本相似度
                   - "partial_ratio": 部分相似度
                   - "token_sort_ratio": 词汇排序相似度
                   - "token_set_ratio": 词汇集合相似度
        
        Returns:
            相似度分数 (0.0-1.0)
        
        Raises:
            ValueError: 当method参数无效时
        """
        if not isinstance(text1, str) or not isinstance(text2, str):
            logger.warning(f"Non-string inputs: {type(text1)}, {type(text2)}")
            return 0.0
        
        if not text1.strip() or not text2.strip():
            return 0.0 if text1.strip() != text2.strip() else 1.0
        
        try:
            scorer = self._get_scorer(method)  # This can raise ValueError
            score = scorer(text1, text2)
            
            # 将0-100的分数转换为0.0-1.0
            return score / 100.0
            
        except ValueError:
            # Re-raise ValueError for invalid method
            raise
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def is_similar(self, text1: str, text2: str, method: Optional[str] = None, threshold: Optional[float] = None) -> bool:
        """
        判断两个文本是否相似
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            method: 相似度计算方法
            threshold: 相似度阈值，如果为None则使用初始化时的阈值
        
        Returns:
            是否相似
        """
        if threshold is None:
            threshold = self.similarity_threshold
        
        similarity = self.calculate_similarity(text1, text2, method)
        return similarity >= threshold
    
    def find_most_similar(self, query: str, choices: List[str], method: Optional[str] = None, limit: int = 5) -> List[Tuple[str, float]]:
        """
        在选择列表中找到与查询文本最相似的项目
        
        Args:
            query: 查询文本
            choices: 候选文本列表
            method: 相似度计算方法
            limit: 返回结果的最大数量
        
        Returns:
            相似度最高的文本列表，每个元素为(文本, 相似度分数)的元组
        """
        if not isinstance(query, str) or not query.strip():
            return []
        
        if not choices:
            return []
        
        try:
            scorer = self._get_scorer(method)
            results = process.extract(query, choices, scorer=scorer, limit=limit)
            # 将分数从0-100转换为0.0-1.0
            return [(text, score / 100.0) for text, score in results]
            
        except Exception as e:
            logger.error(f"Error finding most similar: {e}")
            return []
    
    def deduplicate_by_content(self, items: List[Dict[str, Any]], content_key: str = "content", 
                              score_key: str = "score", method: Optional[str] = None, 
                              threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        根据内容相似度去除重复项，保留分数最高的项目
        
        Args:
            items: 包含内容和分数的字典列表
            content_key: 内容字段的键名
            score_key: 分数字段的键名
            method: 相似度计算方法
            threshold: 相似度阈值，如果为None则使用初始化时的阈值
        
        Returns:
            去重后的项目列表，按分数降序排列
        """
        if not items:
            return []
        
        if threshold is None:
            threshold = self.similarity_threshold
        
        # 按分数降序排序，确保高分项目优先保留
        sorted_items = sorted(items, key=lambda x: x.get(score_key, 0), reverse=True)
        
        deduplicated = []
        
        for current_item in sorted_items:
            current_content = current_item.get(content_key, "")
            
            if not isinstance(current_content, str):
                logger.warning(f"Non-string content found: {type(current_content)}")
                continue
            
            # 检查是否与已保留的项目重复
            is_duplicate = False
            for kept_item in deduplicated:
                kept_content = kept_item.get(content_key, "")
                
                if self.is_similar(current_content, kept_content, method, threshold):
                    is_duplicate = True
                    logger.debug(f"Found duplicate content (similarity: {self.calculate_similarity(current_content, kept_content, method):.3f}): '{current_content[:30]}...' vs '{kept_content[:30]}...'")
                    break
            
            if not is_duplicate:
                deduplicated.append(current_item)
        
        logger.debug(f"Deduplication completed: {len(items)} -> {len(deduplicated)} items")
        return deduplicated
    
    def deduplicate_texts(self, texts: List[str], method: Optional[str] = None, 
                         threshold: Optional[float] = None) -> List[str]:
        """
        去除文本列表中的重复项
        
        Args:
            texts: 文本列表
            method: 相似度计算方法
            threshold: 相似度阈值，如果为None则使用初始化时的阈值
        
        Returns:
            去重后的文本列表
        """
        if not texts:
            return []
        
        if threshold is None:
            threshold = self.similarity_threshold
        
        deduplicated = []
        
        for current_text in texts:
            if not isinstance(current_text, str):
                logger.warning(f"Non-string text found: {type(current_text)}")
                continue
            
            # 检查是否与已保留的文本重复
            is_duplicate = False
            for kept_text in deduplicated:
                if self.is_similar(current_text, kept_text, method, threshold):
                    is_duplicate = True
                    logger.debug(f"Found duplicate text (similarity: {self.calculate_similarity(current_text, kept_text, method):.3f}): '{current_text[:50]}...' vs '{kept_text[:50]}...'")
                    break
            
            if not is_duplicate:
                deduplicated.append(current_text)
        
        logger.debug(f"Text deduplication completed: {len(texts)} -> {len(deduplicated)} items")
        return deduplicated