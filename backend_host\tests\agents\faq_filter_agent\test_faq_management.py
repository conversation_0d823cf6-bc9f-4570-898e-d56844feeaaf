#!/usr/bin/env python3
"""
测试FAQ管理系统

测试FAQRepoManager和FAQDataParser的集成功能，验证新架构的正确性。
"""

import json
import argparse
import logging

from ai_app.services.faq_management.faq_repo_manager import (
    initialize_faq_repo_manager, 
    cleanup_faq_repo_manager
)
from ai_app.config import config, get_logging_config

def main():
    parser = argparse.ArgumentParser(description='测试FAQ管理系统')
    parser.add_argument('test_type', 
                       choices=['markdown', 'answer', 'tree', 'raw'], 
                       help='要测试的功能')
    parser.add_argument('--channel', type=str, default='common', help='指定测试的channel')
    parser.add_argument('--max-depth', type=int, default=-1, help='分类结构最大深度')
    parser.add_argument('--key-path', type=str, help='测试查找的key_path')
    parser.add_argument('-v', '--verbose', action='store_true', help='启用详细日志')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.info("Verbose logging enabled.")
    logging.config.dictConfig(get_logging_config(cli_verbose_flag=args.verbose))
    
    try:
        # 初始化仓库管理器
        repo_manager = initialize_faq_repo_manager(config.faq_excel_path)
        
        # 创建解析器
        data_parser = repo_manager.create_parser(channel=args.channel)
        
        if args.test_type == 'raw':
            print("测试原始数据...")
            #print(json.dumps(data_parser.faq_data, indent=2, ensure_ascii=False))
            results = []
            data_parser.collect_answers_recursively(all_answers=results)
            print(json.dumps(results, indent=2, ensure_ascii=False))
        elif args.test_type == 'markdown':
            print("测试分类结构生成...")
            markdown = data_parser.get_category_structure_markdown(max_depth=args.max_depth)
            print(markdown)

        elif args.test_type == 'tree':
            print("测试树形结构生成...")
            tree_output = data_parser.get_category_structure_tree(max_depth=args.max_depth)
            print(tree_output)

        elif args.test_type == 'answer':
            print(f"测试查找key_path: {args.key_path}")
            if args.key_path.endswith('.'):
                result = data_parser.get_answers_by_key_path(args.key_path)
            else:
                result = data_parser.get_answer_by_key_path(args.key_path)
            if result is None:
                print("<No answer found or path invalid/non-leaf>")
            else:
                print(json.dumps(result, indent=2, ensure_ascii=False))
        
        
        print("✅ 解析器功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 解析器功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        cleanup_faq_repo_manager()

if __name__ == "__main__":
    main()
