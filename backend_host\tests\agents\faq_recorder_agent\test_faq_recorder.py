#!/usr/bin/env python3
"""
FAQ录入代理测试脚本

提供命令行测试工具，用于测试FAQ录入代理的各个功能。
"""

import sys
import os
import time
import argparse
import asyncio
import json
import logging
from typing import Dict, Any

from ai_app.models.faq_recorder import (
    FAQRecorderQuestionAnswer,
    FAQRecorderAnalyzeRequest,
    FAQRecorderCategoriesRequest,
    FAQRecorderSubmitRequest
)
from ai_app.agents.faq_recorder_agent.agent import FAQRecorderAgent
from ai_app.services.faq_management.faq_repo_manager import initialize_faq_repo_manager, cleanup_faq_repo_manager
from ai_app.config import config, get_logging_config

def create_test_qa_pair() -> FAQRecorderQuestionAnswer:
    """创建测试问答对"""
    return FAQRecorderQuestionAnswer(
        question="用户忘记密码了怎么办？",
        answer="用户可以通过以下方式重置密码：1. 点击登录页面的'忘记密码'链接；2. 输入注册时的邮箱地址；3. 查收邮件并点击重置链接；4. 设置新密码。如果仍有问题，请联系客服。",
        question_example="我的密码忘了，如何找回？"
    )


async def test_analyze_qa_pair(model_platform: str = "volcano"):
    """测试问答对分析功能"""
    print(f"=== 测试问答对分析功能 (平台: {model_platform}) ===")
    
    try:
        # 创建测试数据
        qa_pair = create_test_qa_pair()
        request = FAQRecorderAnalyzeRequest(
            qa_pair=qa_pair,
            channel=None,
            service=model_platform
        )
        
        # 初始化代理
        agent = FAQRecorderAgent(model_platform=model_platform, channel=None)
        
        # 执行分析
        print("正在分析问答对...")
        response = await agent.analyze_qa_pair(request)
        
        # 显示结果
        print(f"分析结果:")
        print(f"  响应码: {response.response_code}")
        print(f"  响应消息: {response.response_text}")
        
        if response.examine_result:
            print(f"  LLM分析:")
            print(f"    主题领域: {response.examine_result.get('topic_domain', 'N/A')}")
            print(f"    问题类型: {response.examine_result.get('question_type', 'N/A')}")
            print(f"    置信度: {response.examine_result.get('confidence', 'N/A')}")
        
        print(f"  分类推荐数量: {len(response.recommended_categories)}")
        for i, rec in enumerate(response.recommended_categories, 1):
            print(f"    推荐{i}: {' > '.join(rec.category_path)} (置信度: {rec.confidence:.2f})")
            print(f"           理由: {rec.reason}")
        
        if response.quality_check:
            print(f"  质量检查:")
            print(f"    通过: {response.quality_check.is_valid}")
            print(f"    警告数: {len(response.quality_check.warnings)}")
            for i, warning in enumerate(response.quality_check.warnings, 1):
                print(f"      警告{i}: {warning}")
            print(f"    错误数: {len(response.quality_check.errors)}")
            for i, error in enumerate(response.quality_check.errors, 1):
                print(f"      错误{i}: {error}")
        
        print("分析测试完成 ✓")
        return True
        
    except Exception as e:
        print(f"分析测试失败: {e}")
        return False


def test_categories_structure():
    """测试分类结构获取功能"""
    print("=== 测试分类结构获取功能 ===")
    
    try:
        # 创建请求
        request = FAQRecorderCategoriesRequest(
            channel=None,
            max_depth=3
        )
        
        # 初始化代理
        agent = FAQRecorderAgent(channel=None)
        
        # 获取分类结构
        print("正在获取分类结构...")
        response = agent.get_categories_structure(request)
        
        # 显示结果
        print(f"分类结构获取结果:")
        print(f"  响应码: {response.response_code}")
        print(f"  总分类数: {response.total_categories}")
        print(f"  总答案数: {response.total_answers}")
        print(f"  根分类数: {len(response.category_tree)}")
        
        # 显示前几个分类
        print("  主要分类:")
        for i, node in enumerate(response.category_tree[:5], 1):
            print(f"    {i}. {node.name} (层级{node.level}, 答案数: {node.answer_count})")
            for child in node.children[:3]:
                print(f"       - {child.name} (答案数: {child.answer_count})")
        
        print("分类结构测试完成 ✓")
        return True
        
    except Exception as e:
        print(f"分类结构测试失败: {e}")
        return False


async def test_submit_faq_entry():
    """测试FAQ录入提交功能"""
    print("=== 测试FAQ录入提交功能 ===")
    
    try:
        # 创建测试数据
        qa_pair = create_test_qa_pair()
        request = FAQRecorderSubmitRequest(
            qa_pair=qa_pair,
            category_path=["账号管理", "密码相关", "找回密码"],
            channel=None,
        )
        
        # 初始化代理
        agent = FAQRecorderAgent(channel=None)
        
        # 执行提交
        print("正在提交FAQ录入...")
        response = await agent.submit_faq_entry(request)
        
        # 显示结果
        print(f"提交结果:")
        print(f"  响应码: {response.response_code}")
        print(f"  响应消息: {response.response_text}")
        
        if response.quality_check:
            print(f"  质量检查:")
            print(f"    通过: {response.quality_check.is_valid}")
            if response.quality_check.warnings:
                print(f"    警告: {', '.join(response.quality_check.warnings)}")
            if response.quality_check.errors:
                print(f"    错误: {', '.join(response.quality_check.errors)}")
        
        if response.excel_info:
            print(f"  Excel信息:")
            print(f"    文件存在: {response.excel_info.get('exists', False)}")
            print(f"    文件路径: {response.excel_info.get('path', 'N/A')}")
            if 'sheets' in response.excel_info:
                for sheet_name, sheet_info in response.excel_info['sheets'].items():
                    if isinstance(sheet_info, dict) and 'rows' in sheet_info:
                        print(f"    工作表 {sheet_name}: {sheet_info['rows']} 行")
        
        print("提交测试完成 ✓")
        #time.sleep(100)
        return True
        
    except Exception as e:
        print(f"提交测试失败: {e}")
        return False


def test_individual_components():
    """测试各个独立组件"""
    print("=== 测试独立组件 ===")
    
    try:
        from ai_app.agents.faq_recorder_agent.rule_based_recommender import FAQRuleBasedRecommender
        from ai_app.agents.faq_recorder_agent.quality_checker import FAQQualityChecker
        from ai_app.agents.faq_recorder_agent.excel_writer import FAQExcelWriter
        from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager
        
        # 获取FAQ解析器
        repo_manager = get_faq_repo_manager()
        parser = repo_manager.create_parser()
        
        # 测试分类推荐器
        print("测试分类推荐器...")
        recommender = FAQRuleBasedRecommender(parser)
        qa_pair = create_test_qa_pair()
        recommendations = recommender.recommend_categories(qa_pair, max_recommendations=3)
        print(f"  生成了 {len(recommendations)} 个推荐")
        for rec in recommendations:
            print(f"    {' > '.join(rec.category_path)} (置信度: {rec.confidence:.2f})")
        
        # 测试质量检测器
        print("测试质量检测器...")
        checker = FAQQualityChecker(parser)
        #category_path = ["账号管理", "密码相关"] if recommendations else ["测试分类"]
        #quality_result = checker.check_quality(qa_pair, [category_path])
        quality_result = checker.check_quality(qa_pair, [['账号'], ['账号', '密码相关', '找回密码'], ['账号', '密码相关']])
        print(f"  质量检查通过: {quality_result.is_valid}")
        print(f"  警告数量: {len(quality_result.warnings)}")
        print(f"  错误数量: {len(quality_result.errors)}")
        
        # 测试Excel写入器（只获取信息，不实际写入）
        print("测试Excel写入器...")
        writer = FAQExcelWriter(config.faq_excel_path)
        excel_info = writer.get_excel_info()
        print(f"  Excel文件存在: {excel_info.get('exists', False)}")
        print(f"  Excel文件路径: {excel_info.get('path', 'N/A')}")
        
        print("独立组件测试完成 ✓")
        return True
        
    except Exception as e:
        print(f"独立组件测试失败: {e}")
        return False


async def run_all_tests(model_platform: str = "volcano"):
    """运行所有测试"""
    print("=" * 60)
    print("FAQ录入代理完整测试")
    print("=" * 60)
    
    # 初始化FAQ仓库管理器
    try:
        print("初始化FAQ仓库管理器...")
        initialize_faq_repo_manager(config.faq_excel_path)
        print("FAQ仓库管理器初始化成功 ✓")
    except Exception as e:
        print(f"FAQ仓库管理器初始化失败: {e}")
        return False
    
    results = []
    
    try:
        # 运行各项测试
        results.append(("独立组件测试", test_individual_components()))
        results.append(("分类结构测试", test_categories_structure()))
        results.append(("问答分析测试", await test_analyze_qa_pair(model_platform)))
        results.append(("录入提交测试", await test_submit_faq_entry()))
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✓ PASS" if result else "✗ FAIL"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试都通过了！")
            return True
        else:
            print("⚠️  部分测试失败，请检查日志")
            return False
            
    finally:
        cleanup_faq_repo_manager()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FAQ录入代理测试工具")
    parser.add_argument(
        "test_type",
        choices=["all", "analyze", "categories", "submit", "components"],
        help="测试类型"
    )
    parser.add_argument(
        "-v",
        "--verbose",
        action="store_true",
        help="启用详细日志"
    )
    parser.add_argument(
        "--platform",
        choices=["google", "openai"],
        default="google",
        help="LLM平台选择"
    )
    parser.add_argument(
        "--question", "-q",
        help="自定义测试问题"
    )
    parser.add_argument(
        "--answer", "-a",
        help="自定义测试答案"
    )
    parser.add_argument(
        "--category",
        nargs="+",
        help="自定义分类路径（多个参数）"
    )
    
    args = parser.parse_args()    
    if args.verbose:
        logging.info("Verbose logging enabled.")
    logging.config.dictConfig(get_logging_config(cli_verbose_flag=args.verbose))
    
    # 如果提供了自定义问答，创建自定义测试数据
    if args.question and args.answer:
        global create_test_qa_pair
        def create_custom_qa_pair():
            return FAQRecorderQuestionAnswer(
                question=args.question,
                answer=args.answer,
                question_example=args.question
            )
        create_test_qa_pair = create_custom_qa_pair
    
    async def run_tests():
        # 初始化FAQ仓库
        initialize_faq_repo_manager(config.faq_excel_path)
        #time.sleep(300)
        try:
            if args.test_type == "all":
                return await run_all_tests(args.platform)
            elif args.test_type == "analyze":
                return await test_analyze_qa_pair(args.platform)
            elif args.test_type == "categories":
                return test_categories_structure()
            elif args.test_type == "submit":
                return await test_submit_faq_entry()
            elif args.test_type == "components":
                return test_individual_components()
        finally:
            cleanup_faq_repo_manager()
    
    try:
        success = asyncio.run(run_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()