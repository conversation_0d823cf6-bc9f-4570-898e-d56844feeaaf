import React, { useState } from 'react';
import {channelOptions} from '../common/ChannelSelector';

/**
 * 提交确认界面组件
 * @param {Object} props
 * @param {Object} props.qaData - 问答数据
 * @param {Array} props.categoryPath - 选择的分类路径
 * @param {string} props.channel - 目标渠道
 * @param {Object} props.qualityCheck - 质量检查结果
 * @param {Function} props.onSubmit - 提交回调
 * @param {boolean} props.isSubmitting - 是否正在提交
 * @param {Object} props.submitResults - 提交结果
 */
function SubmitConfirmation({ 
  qaData, 
  categoryPath, 
  channel, 
  qualityCheck, 
  onSubmit, 
  isSubmitting, 
  submitResults 
}) {
  const [showPreview, setShowPreview] = useState(false);

  // 检查是否有质量问题
  const hasWarnings = qualityCheck?.warnings?.length > 0;
  const hasErrors = qualityCheck?.errors?.length > 0;
  const hasDuplicates = qualityCheck?.duplicate_questions?.length > 0;
  
  const hasQualityIssues = hasWarnings || hasErrors || hasDuplicates;
  
  // 判断是否可以提交
  const canSubmit = categoryPath && categoryPath.length > 0 && !isSubmitting;

  // 获取渠道显示名称
  const getChannelDisplayName = (channel) => {
    const channelName = channelOptions.find((item) => item.value === channel)?.label || channel;
    if (channelName === null || channelName === '') {
      return 'common';
    }
    return channelName;
  };

  // 处理提交
  const handleSubmit = () => {
    if (!canSubmit) return;
    
    onSubmit();
  };

  // 如果已经提交成功，显示结果
  if (submitResults) {
    return (
      <div className="submit-confirmation">
        <div className={`submit-result ${submitResults.success ? 'success' : 'error'}`}>
          <div className="result-icon">
            {submitResults.success ? '✅' : '❌'}
          </div>
          <div className="result-content">
            <h3>
              {submitResults.success ? 'FAQ录入成功！' : 'FAQ录入失败'}
            </h3>
            <p className="result-message">
              {submitResults.message || '操作已完成'}
            </p>
            
            {submitResults.success && (
              <div className="success-details">
                <div className="detail-item">
                  <span className="detail-label">录入分类:</span>
                  <span className="detail-value">{categoryPath.join(' › ')}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">目标渠道:</span>
                  <span className="detail-value">{getChannelDisplayName(channel)}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">录入时间:</span>
                  <span className="detail-value">{new Date().toLocaleString()}</span>
                </div>
              </div>
            )}
            
            <div className="result-actions">
              <button 
                className="action-button primary"
                onClick={() => {
                  // 滚动到页面顶端
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                  // 重新加载页面
                  setTimeout(() => {
                    window.location.reload();
                  }, 500);
                }}
              >
                录入新的FAQ
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="submit-confirmation">
      <div className="confirmation-header">
        <h3>📋 提交确认</h3>
        <p>请确认以下信息无误后提交FAQ录入</p>
      </div>

      {/* 录入信息预览 */}
      <div className="confirmation-preview">
        <div className="preview-section">
          <div className="section-header">
            <span className="section-title">📝 录入信息</span>
            <button 
              className="toggle-preview"
              onClick={() => setShowPreview(!showPreview)}
            >
              {showPreview ? '收起详情' : '展开详情'}
            </button>
          </div>
          
          <div className="preview-summary">
            <div className="summary-item">
              <span className="summary-label">分类路径:</span>
              <span className="summary-value category-path">
                {categoryPath.join(' › ')}
              </span>
            </div>
            <div className="summary-item">
              <span className="summary-label">目标渠道:</span>
              <span className="summary-value">{getChannelDisplayName(channel)}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">问题长度:</span>
              <span className="summary-value">{qaData.question.length} 字符</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">答案长度:</span>
              <span className="summary-value">{qaData.answer.length} 字符</span>
            </div>
          </div>

          {showPreview && (
            <div className="preview-details">
              <div className="detail-group">
                <h4>用户问题:</h4>
                <div className="detail-content question">
                  {qaData.question}
                </div>
              </div>
              
              <div className="detail-group">
                <h4>标准答案:</h4>
                <div className="detail-content answer">
                  {qaData.answer}
                </div>
              </div>
              
              {qaData.questionExample && (
                <div className="detail-group">
                  <h4>问题示例:</h4>
                  <div className="detail-content example">
                    {qaData.questionExample}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 质量检查状态 */}
      {qualityCheck && (
        <div className="quality-status">
          <div className="status-header">
            <span className="status-title">🔍 质量检查状态</span>
          </div>
          
          <div className={`status-summary ${hasErrors ? 'error' : hasQualityIssues ? 'warning' : 'success'}`}>
            <div className="status-icon">
              {hasErrors ? '❌' : hasQualityIssues ? '⚠️' : '✅'}
            </div>
            <div className="status-text">
              {hasErrors && '发现错误，建议修正后再提交'}
              {!hasErrors && hasQualityIssues && '发现警告，可以选择强制提交'}
              {!hasErrors && !hasQualityIssues && '质量检查通过，可以安全提交'}
            </div>
          </div>

          {hasQualityIssues && (
            <div className="quality-issues-summary">
              {hasErrors && (
                <div className="issue-count error">
                  ❌ {qualityCheck.errors.length} 个错误
                </div>
              )}
              {hasWarnings && (
                <div className="issue-count warning">
                  ⚠️ {qualityCheck.warnings.length} 个警告
                </div>
              )}
              {hasDuplicates && (
                <div className="issue-count duplicate">
                  🔄 {qualityCheck.duplicate_questions.length} 个重复问题
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* 提交按钮 */}
      <div className="submit-actions">
        <button
          className={`submit-button ${canSubmit ? 'primary' : 'disabled'}`}
          onClick={handleSubmit}
          disabled={!canSubmit}
        >
          {isSubmitting ? (
            <>
              <span className="loading-spinner"></span>
              正在提交...
            </>
          ) : (
            '✅ 提交FAQ'
          )}
        </button>

        <button
          className="cancel-button"
          onClick={() => {
            // 滚动到页面顶端
            window.scrollTo({ top: 0, behavior: 'smooth' });
            // 重新加载页面
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }}
          disabled={isSubmitting}
        >
          取消重新开始
        </button>
      </div>

      {/* 提交说明 */}
      <div className="submit-notes">
        <div className="note-title">📌 提交说明:</div>
        <ul className="note-list">
          <li>提交后FAQ将被录入到Excel文件中</li>
          <li>系统会自动创建备份以确保数据安全</li>
          <li>新的分类路径将自动创建</li>
          <li>录入后可以立即在FAQ查询中使用</li>
        </ul>
      </div>
    </div>
  );
}

export default SubmitConfirmation;