@echo off

rem 使用 widdershins 在当前目录将 openapi.json 转换为 openapi.md
rem 使用前需要安装 widdershins：npm install -g widdershins
rem 使用 --language_tabs 参数指定语言标签
rem 使用 --summary 参数生成摘要

echo Converting openapi.json to openapi.md using widdershins...
widdershins openapi.json -o openapi.md --language_tabs shell:Shell java:Java --summary --omitHeader
rem widdershins openapi.json -o openapi.md --summary
echo Conversion complete. Output: openapi.md
pause 