import json
import jinja2
from typing import List, Dict, Any, Tuple, Optional

from ai_app.models.chat import ChatModelUsage
from ai_app.shared.exceptions import LLMAPIError, LLMResponseError, EmbeddingCollectionNotFoundError
from ai_app.agents.shared.llm_impl.base_llm_impl import BaseLLMImpl, DEFAULT_TIMEOUT
from ai_app.agents.shared.llm_impl.rerank_impl import RerankImpl
from ai_app.agents.shared.llm_impl.embedding_impl import EmbeddingImpl
from ai_app.services.faq_management.data_parser import FAQDataParser
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

class QueryRewriteClient: # 不再继承 VolcanoLLMClient
    """使用配置的 LLM 实现异步重写查询。"""

    def __init__(self, llm_client: BaseLLMImpl, prompt_template: str): # Updated type hint
        """初始化客户端。

        Args:
            llm_client: 一个实现了 BaseLLMImpl 接口的实例。
            prompt_template: rewrite_prompt.md 的内容模板。
        """

        self.llm_client = llm_client # 存储 LLM 客户端实例
        self.prompt_template = prompt_template
        # 注意：不再需要 super().__init__()

    async def rewrite_query(
        self,
        input_data: Dict[str, Any],
        timeout: float = DEFAULT_TIMEOUT
    ) -> Tuple[str, ChatModelUsage]:
        """异步调用 LLM API 来重写查询。

        Args:
            input_data: 包含 'conversation' 和 'context' 的字典。
            timeout: 请求超时时间 (秒)。

        Returns:
            Tuple[str, ChatModelUsage]: 包含重写后的查询字符串及 ChatModelUsage。

        Raises:
            LLMAPIError: 如果 API 调用失败。
            LLMResponseError: 如果输入数据无效、提示格式化失败或 API 响应格式不正确。
        """
        # 1. 准备 Prompt
        try:
            conversation = input_data.get('conversation')
            context = input_data.get('context')

            if not isinstance(conversation, list):
                logger.error(f"Invalid 'conversation' format: Expected list, got {type(conversation)}")
                raise LLMResponseError("Invalid input data: 'conversation' must be a list.")
            if not isinstance(context, dict):
                 logger.error(f"Invalid 'context' format: Expected dict, got {type(context)}")
                 raise LLMResponseError("Invalid input data: 'context' must be a dictionary.")

            system_prompt_content = self.prompt_template
            messages = [
                {"role": "system", "content": system_prompt_content},
                {"role": "user", "content": json.dumps(input_data, ensure_ascii=False)}
            ]

        except LLMResponseError:
            raise
        except Exception as e:
            logger.error(f"Error preparing data for query rewrite: {e}", exc_info=True)
            raise LLMAPIError(f"Failed to prepare data for rewrite: {e}") from e

        # 2. 调用传入的 LLM 客户端的 chat_completion 方法
        try:
            content, usage, _ = await self.llm_client.chat_completion( # 调用注入的客户端实例
                messages=messages,
                timeout=timeout,
                temperature=0.1, # Low temp for deterministic rewrite
                #response_format={"type": "json_object"} # Request JSON
            )
        except (LLMAPIError, LLMResponseError):
             raise # Re-raise API or response errors
        except Exception as e:
             # Log the specific LLM client implementation type for better debugging
             client_type = type(self.llm_client).__name__
             logger.exception(f"Unexpected error calling rewrite API via {client_type}: {e}")
             raise LLMAPIError(f"Unexpected error during API call via {client_type}: {e}") from e

        # 3. 返回重写后的查询和使用信息
        model_id = usage.model_id if usage else "unknown"
        logger.info(f"Successfully rewrote query using model {model_id} via {type(self.llm_client).__name__}.")
        return content, usage

class FAQClassifierClient:
    """使用配置的 LLM 实现异步进行问题分类。"""

    def __init__(self, llm_client: BaseLLMImpl, prompt_template: str): # Updated type hint
        """初始化客户端。

        Args:
            llm_client: 一个实现了 BaseLLMImpl 接口的实例。
            prompt_template: classify_prompt.md 的内容模板 (Jinja2 格式)。
        """

        self.llm_client = llm_client # 存储 LLM 客户端实例
        self.prompt_template_str = prompt_template # Store the raw template string
        # Pre-compile the Jinja2 template
        try:
            self.jinja_template = jinja2.Template(self.prompt_template_str)
        except jinja2.exceptions.TemplateSyntaxError as e:
             logger.error(f"Invalid Jinja2 template syntax: {e}")
             raise LLMResponseError(f"Invalid Jinja2 template syntax: {e}") from e
        # 注意：不再需要 super().__init__()

    async def classify_query(
        self,
        rewritten_query: str,
        faq_structure_md: str,
        timeout: float = DEFAULT_TIMEOUT
    ) -> Tuple[List[Dict[str, Any]], ChatModelUsage, Optional[str]]: # 返回类型调整为 List of Dicts
        """异步调用 LLM API 对重写后的查询进行分类。

        Args:
            rewritten_query: 重写后的查询字符串。
            faq_structure_md: Markdown 格式的 FAQ 目录结构。
            timeout: 请求超时时间 (秒)。

        Returns:
            Tuple[List[Dict[str, Any]], ChatModelUsage, Optional[str]]: 一个包含以下字段的元组:
                - 分类结果 ({'category_key_path', 'reason（可选的）'}) 的列表
                - ChatModelUsage 对象
                - 思考过程 (Optional[str])

        Raises:
            LLMAPIError: 如果 API 调用失败。
            LLMResponseError: 如果提示格式化失败或 API 响应格式不正确。
        """
        # 1. 构建 Prompt using Jinja2
        try:
            system_prompt_content = self.jinja_template.render(faq_structure=faq_structure_md, faq_retrieve_num=3)
            messages = [
                {"role": "system", "content": system_prompt_content},
                {"role": "user", "content": rewritten_query}
            ]
        except jinja2.exceptions.UndefinedError as e:
             logger.error(f"Jinja2 rendering error: Undefined variable {e}.", exc_info=True)
             raise LLMResponseError(f"Failed to render classification prompt: Undefined variable {e}.") from e
        except Exception as e:
            logger.error(f"Error preparing prompt for FAQ classification: {e}", exc_info=True)
            raise LLMResponseError(f"Failed to prepare classification prompt: {e}") from e

        # 2. 调用传入的 LLM 客户端的 chat_completion 方法
        try:
            content, usage, response_data = await self.llm_client.chat_completion( # 调用注入的客户端实例
                messages=messages,
                timeout=timeout,
                temperature=0.1, # Low temp for classification
                #response_format={"type": "json_object"} # Request JSON
            )
        except (LLMAPIError, LLMResponseError):
            raise # Re-raise API or response errors
        except Exception as e:
             client_type = type(self.llm_client).__name__
             logger.exception(f"Unexpected error calling classification API via {client_type}: {e}")
             raise LLMAPIError(f"Unexpected error during API call via {client_type}: {e}") from e

        # 3. 解析响应 (与之前类似)
        # content 已经是移除 wrapper 后的
        results = []
        for line in content.split('\n'):
            line = self.llm_client.clean_content(line)
            if line and len(line) > 0:
                results.append({
                    'category_key_path': line,
                })
        model_id = usage.model_id if usage else "unknown"
        logger.info(f"Successfully classified query using model {model_id} via {type(self.llm_client).__name__}")
        return results, usage, self.llm_client.get_thinking_content(response_data) # 直接返回解析后的列表
    
class FAQRetrieveClient:
    """使用配置的 EmbeddingImpl 实现异步进行问题检索。"""

    def __init__(self, llm_client: EmbeddingImpl):
        """初始化客户端。

        Args:
            llm_client: 一个实现了 EmbeddingImpl 接口的实例。
        """
        self.llm_client = llm_client

    async def retrieve_from_query(
        self,
        query: str,
        top_n: int = 10
    ) -> Tuple[List[Dict[str, Any]], ChatModelUsage]:
        """异步调用向量数据库检索相关问题。

        Args:
            query: 查询字符串。
            top_n: 返回的结果数量。

        Returns:
            Tuple[List[Dict[str, Any]], ChatModelUsage]: 一个包含以下字段的元组:
                - 检索结果 ({'key_path', 'reason'}) 的列表
                - ChatModelUsage 对象

        Raises:
            LLMAPIError: 如果 API 调用失败。
            LLMResponseError: 如果 API 响应格式不正确。
            EmbeddingCollectionNotFoundError: 如果知识库不存在。
        """
        # 1. 调用 EmbeddingImpl 进行向量检索
        try:
            results, usage, _ = await self.llm_client.search_knowledge(
                query=query,
                top_n=top_n
            )
        except (LLMAPIError, LLMResponseError, EmbeddingCollectionNotFoundError):
            raise # Re-raise API or response errors
        except Exception as e:
            client_type = type(self.llm_client).__name__
            logger.exception(f"Unexpected error calling retrieve API via {client_type}: {e}")
            raise LLMAPIError(f"Unexpected error during API call via {client_type}: {e}") from e

        # 2. 对results根据得分从高到低排序
        results.sort(key=lambda x: x['score'], reverse=True)

        model_id = usage.model_id if usage else "unknown"
        logger.info(f"Successfully retrieved {len(results)} results using model {model_id} from {self.llm_client.get_doc_name()} via {type(self.llm_client).__name__}")
        return results, usage

class FAQRerankClient:
    def __init__(self, llm_client: RerankImpl):
        """
        初始化 FAQRerankClient。

        Args:
            llm_client: RerankImpl 的一个实例。
        """
        self.llm_client = llm_client # This is an instance of RerankImpl

    async def rerank_retrieve_results(
        self, query: str, retrieve_results: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], ChatModelUsage]:
        """
        根据查询对检索到的结果列表进行重排序。
        'final_answer' 为 None 的项将被排除在 rerank 之外，并获得最低分数。
        新的 'score' 字段将被添加到每个结果字典中。

        Args:
            query: 用于重排序的查询字符串。
            retrieve_results: 字典列表，每个字典代表一个检索结果，
                              预计包含 'final_answer' 等键。

        Returns:
            一个元tuple，包含:
            - 按新的 'score' 降序排序的 retrieve_results 列表。
            - 一个 ChatModelUsage 对象，详细说明 reranking 模型的 token 使用情况。
        """
        
        results_to_rerank_candidates = [] # 存储具有有效 final_answer 的原始 result 字典
        documents_for_rerank_api = []     # 存储 'final_answer' 字符串以供 RerankImpl 使用
        excluded_results = []             # 存储因 final_answer 为 None 而被排除的 result

        for i, result_item in enumerate(retrieve_results):
            final_answer = result_item.get('final_answer')
            if final_answer and isinstance(final_answer, str) and final_answer.strip():
                results_to_rerank_candidates.append(result_item) # 保存原始字典的引用

                # 构造文档
                description_path_to_string = FAQDataParser.description_path_to_string(result_item['desc_path'])
                question_example = result_item.get('question_example')
                document = f"""
## 问题类别
`{description_path_to_string}`
## 问题示例
`{question_example}`
                """
                documents_for_rerank_api.append(document)
            else:
                logger.debug(f"result item {i} (content: {str(result_item)[:100]}...) excluded from rerank because final_answer is empty or invalid.")
                excluded_results.append(result_item)

        if not documents_for_rerank_api:
            logger.info("No documents with final_answer found for rerank. All results will receive default low score.")
            return retrieve_results, None

        rerank_api_output: List[Dict[str, Any]] = []
        query_for_rerank_api = f"""
## 问题描述
```
{query}
```
        """

        try:
            # top_n 设置为实际要 rerank 的文档数
            top_n_for_api = len(documents_for_rerank_api)
            logger.debug(f"Calling RerankImpl to rerank {len(documents_for_rerank_api)} documents. Query: {query[:100]}... , top_n: {top_n_for_api}")
            
            rerank_api_output, usage, _ = await self.llm_client.rerank(
                query=query_for_rerank_api,
                documents=documents_for_rerank_api,
                top_n=top_n_for_api # RerankImpl 会处理 top_n > 文档数的情况
            )
            logger.info(f"RerankImpl returned {len(rerank_api_output)} results(original retrieve_results size: {len(retrieve_results)}). Requested top_n: {top_n_for_api}. Usage: {usage.input_tokens} input tokens to {self.llm_client.model_id}.")

        except LLMAPIError as e:
            raise
        except Exception as e:
            raise LLMAPIError(f"Unexpected error during API call via {type(self.llm_client).__name__}: {e}") from e
            
        # results_to_rerank_candidates 中的项已经有 0.0 的分数。
        # 现在更新从 rerank API 返回的分数。
        for reranked_item in rerank_api_output:
            original_doc_index = reranked_item.get('index')
            score = reranked_item.get('score')

            if original_doc_index is None or score is None:
                logger.warning(f"Rerank API returned item missing 'index' or 'score': {reranked_item}. Skipping this item.")
                continue

            if 0 <= original_doc_index < len(results_to_rerank_candidates):
                results_to_rerank_candidates[original_doc_index]['score'] = score
            else:
                logger.warning(
                    f"Rerank API returned an out-of-bounds index: {original_doc_index} "
                    f"for a candidate list of size {len(results_to_rerank_candidates)}. Skipping this item."
                )
        
        # retrieve_results 是原始列表的引用，其中的元素（如果被包含在 results_to_rerank_candidates 或 excluded_results 中）
        # 已经被修改（添加了 'score' 字段）。
        # 现在，对整个 retrieve_results 列表进行排序。
        # 所有原始的 result_item 都应该已经通过上述逻辑获得了 'score'
        all_results_sorted = sorted(retrieve_results, key=lambda x: x.get('score', -float('inf')), reverse=True)
        
        return all_results_sorted, usage