import React from 'react';
import './ChannelSelector.css';

// 统一的渠道选项
export const channelOptions = [
  { value: 'zulong', label: 'zulong' },
  { value: 'xiaomi', label: 'xiaomi' },
  { value: 'huawei', label: 'huawei' },
  { value: '苹果', label: '苹果' }
];

/**
 * 一个可复用的渠道选择下拉框组件
 * @param {Object} props
 * @param {string} props.channelName - 当前选中的渠道值
 * @param {Function} props.onChannelChange - 渠道变化时的回调函数
 * @param {boolean} props.disabled - 是否禁用
 * @param {string} [props.displayMode='default'] - 显示模式: 'default' 或 'compact'
 * @param {string} [props.className] - 允许外部传入额外的className
 */
export function ChannelSelector({ channelName, onChannelChange, disabled, displayMode = 'default', className = '' }) {
  const selectElement = (
    <select
      className={`channel-selector ${className}`} // 允许外部class覆盖
      value={channelName}
      onChange={(e) => onChannelChange(e.target.value)}
      disabled={disabled}
    >
      {channelOptions.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );

  if (displayMode === 'compact') {
    return selectElement;
  }

  // 默认模式，带label和wrapper
  return (
    <div className="channel-selector-wrapper">
      <label>渠道:</label>
      {selectElement}
    </div>
  );
}