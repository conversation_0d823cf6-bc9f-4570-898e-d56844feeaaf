"""
Excel 处理工具

从独立脚本中抽离的Excel读写功能，供各个代理共享使用。
"""

import pandas as pd
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

# Excel相关常量
DEFAULT_SHEET_NAME = "common"  # 默认工作表名称
MAX_CATEGORY_LEVELS = 5  # 最大分类层级数
MAX_BACKUPS = 10  # 最大备份文件数量

class ExcelImporter:
    """
    Excel文件读取器，将Excel数据转换为特定的JSON结构。
    """
    
    def __init__(self, excel_file_path: str):
        """
        初始化读取器。

        Args:
            excel_file_path: Excel 文件的路径。
        """
        self.excel_file_path = excel_file_path

    def read_excel(self, sheet_name: str, base_sheet_name: str = None) -> list:
        """
        读取 Excel 文件。

        Args:
            sheet_name: 要读取的工作表名称。
            base_sheet_name: 基础工作表名称，默认为 None。如果指定了基础工作表名称，会先加载基础工作表，然后把sheet_name中的数据合并到基础工作表中。
        """
        try:
            sheet_data = pd.read_excel(self.excel_file_path, sheet_name=sheet_name)
            
            if base_sheet_name is not None:
                base_sheet_data = pd.read_excel(self.excel_file_path, sheet_name=base_sheet_name)
                
            logger.debug(f"Read sheet({sheet_name}) and base sheet({base_sheet_name}) from excel({self.excel_file_path}) successfully.")
        except FileNotFoundError:
            logger.error(f"File not found: {self.excel_file_path}")
            return
        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            return

        # 是否指定了base表用来合并
        if base_sheet_name is not None:
            # 阶段1：处理基础数据
            base_processed_data = self._process_rows(base_sheet_data)

            # 阶段2：处理增量数据
            diff_processed_data = self._process_rows(sheet_data)

            # 阶段3：智能合并两个树结构
            processed_data = self._merge_tree_data(base_processed_data, diff_processed_data)
        else:
            # 单sheet处理
            processed_data = self._process_rows(sheet_data)

        # 最后阶段：统一分配key_path编号
        if processed_data:
            self._assign_key_paths(processed_data)

        return processed_data

    def _merge_tree_data(self, base_processed_data, diff_processed_data):
        """
        智能合并两个树结构，采用overwrite策略。
        使用迭代方式，直接修改base_processed_data。

        Args:
            base_processed_data: 基础树结构数据
            diff_processed_data: 增量树结构数据

        Returns:
            合并后的树结构数据（即修改后的base_processed_data）
        """
        # 1. 迭代提取diff中所有答案节点的路径和candidates
        answer_nodes_to_merge = []
        queue = [(node, [node.get("category_desc", "")]) for node in diff_processed_data]

        while queue:
            current_node, path = queue.pop(0)

            # 如果当前节点有candidates，记录下来
            if "candidates" in current_node:
                answer_nodes_to_merge.append((path, current_node["candidates"]))

            # 将子节点加入队列
            if "sub_category" in current_node and isinstance(current_node["sub_category"], list):
                for sub_node in current_node["sub_category"]:
                    new_path = path + [sub_node.get("category_desc", "")]
                    queue.append((sub_node, new_path))

        # 2. 对每个答案节点，在base中查找或创建对应路径，然后overwrite
        for path, candidates in answer_nodes_to_merge:
            target_node = self._find_or_create_category_path(base_processed_data, path)
            # overwrite策略：直接替换candidates
            target_node["candidates"] = candidates

        logger.debug(f"Merge completed, processed {len(answer_nodes_to_merge)} answer nodes.")
        return base_processed_data

    def _process_row(self, row):
        """
        处理单行数据。
        """
        row_values = []
        # 遍历，前5列是分类，第6列是答案，第7列是问题示例
        classify_columns = 5
        for col in range(classify_columns + 2):
            # 使用 .iloc 进行位置索引，避免 FutureWarning
            cell_value = row.iloc[col]
            # 使用 pd.notna() 检查是否为有效值 (非 None 和非 NaN)
            if pd.notna(cell_value):
                # 先转换为字符串，再去除首尾空格
                cell_value_str = str(cell_value).strip()
                if len(cell_value_str) > 0:
                    row_values.append(cell_value_str)
                    continue
            
            # 分类列之后，即使是空值也要添加空字符串
            if col >= classify_columns:
                row_values.append("")

        return row_values

    def _assign_key_paths(self, processed_data, parent_key_path=""):
        """
        统一为整个分类树分配key_path编号。

        Args:
            processed_data: 分类树数据列表
            parent_key_path: 父节点的key_path前缀（如""、"1."、"1.1."）
        """
        for index, node in enumerate(processed_data):
            # 为分类节点生成key_path（以.结尾）
            if parent_key_path == "":
                # 根节点
                node_key_path = f"{index + 1}."
            else:
                # 子节点：parent_key_path + 当前索引 + "."
                node_key_path = f"{parent_key_path}{index + 1}."

            node["key_path"] = node_key_path

            # 递归处理子分类
            if "sub_category" in node and isinstance(node["sub_category"], list):
                # 使用当前节点的key_path作为子节点的parent_key_path
                self._assign_key_paths(node["sub_category"], node_key_path)

            # 为candidates分配key_path
            if "candidates" in node and isinstance(node["candidates"], list):
                # 计算candidates的起始编号：子分类数量 + 1
                sub_category_count = len(node.get("sub_category", []))
                base_path = node_key_path.rstrip('.')  # 移除末尾的.

                for candidate_index, candidate in enumerate(node["candidates"]):
                    candidate_key_path = f"{base_path}.{sub_category_count + 1 + candidate_index}"
                    candidate["key_path"] = candidate_key_path

    def _process_rows(self, sheet_data):
        """
        遍历并处理 Excel 文件中的所有行。
        """
        if sheet_data is None:
            logger.error("Data not loaded, please call read_excel() first.")
            return

        # 直接在遍历时构建 processed_data
        processed_data = []
        for _, row in sheet_data.iterrows():
            processed_row_data = self._process_row(row)
            if processed_row_data:
                # ---- 将合并逻辑移到此处 ----
                if len(processed_row_data) < 3:
                    logger.warning(f"Skip invalid row (at least 1 category, 1 answer, 1 question example): {processed_row_data}")
                    continue

                # 处理7列数据结构：前N-2列为keys，倒数第2列为answer，倒数第1列为question_example
                keys = processed_row_data[:-2]
                answer = processed_row_data[-2]
                question_example = processed_row_data[-1]  # question_example（可能为空字符串）

                current_level = processed_data # 从根列表开始

                for i, key in enumerate(keys):
                    found_node = None
                    # 在当前层级查找具有相同 category_desc 的节点
                    for node in current_level:
                        if node.get("category_desc") == key:
                            found_node = node
                            break

                    is_last_key = (i == len(keys) - 1)

                    if found_node:
                        # 找到节点
                        if is_last_key:
                            # 如果是最后一个 key，添加到 candidates 数组
                            if "candidates" not in found_node:
                                found_node["candidates"] = []

                            found_node["candidates"].append({
                                "answer": answer,
                                "question_example": question_example
                            })
                        else:
                            # 如果不是最后一个 key，确保有 sub_category 并进入下一层
                            if "sub_category" not in found_node:
                                found_node["sub_category"] = []
                            # 检查 current_level 是否为 None 或不是列表，如果需要则创建
                            if not isinstance(found_node.get("sub_category"), list):
                                found_node["sub_category"] = []
                            current_level = found_node["sub_category"]
                    else:
                        # 未找到节点，创建新节点
                        new_node = {
                            "category_desc": key
                        }
                        current_level.append(new_node) # 将新节点添加到当前列表

                        if is_last_key:
                            # 如果是最后一个 key，创建 candidates 数组
                            new_node["candidates"] = [{
                                "answer": answer,
                                "question_example": question_example
                            }]
                        else:
                            # 如果不是最后一个 key，添加空的 sub_category 列表，并进入下一层
                            new_node["sub_category"] = []
                            current_level = new_node["sub_category"]
                # ---- 合并逻辑结束 ----

        return processed_data

    def _find_or_create_category_path(self, tree_data, path):
        """
        迭代方式查找或创建分类路径，返回目标节点。

        Args:
            tree_data: 树结构数据列表
            path: 分类路径列表，例如 ["账号", "密码相关", "找回密码"]

        Returns:
            目标分类节点（字典）
        """
        current_level = tree_data
        current_node = None

        for i, category_desc in enumerate(path):
            # 在当前层级查找具有相同 category_desc 的节点
            found_node = None
            for node in current_level:
                if node.get("category_desc") == category_desc:
                    found_node = node
                    break

            if found_node:
                # 找到节点，继续下一层
                current_node = found_node
                if i < len(path) - 1:  # 不是最后一层
                    # 确保有 sub_category 并进入下一层
                    if "sub_category" not in current_node:
                        current_node["sub_category"] = []
                    current_level = current_node["sub_category"]
            else:
                # 未找到节点，创建新节点
                new_node = {
                    "category_desc": category_desc
                }
                current_level.append(new_node)
                current_node = new_node

                if i < len(path) - 1:  # 不是最后一层
                    # 添加空的 sub_category 列表，并进入下一层
                    new_node["sub_category"] = []
                    current_level = new_node["sub_category"]

        return current_node

    def dump_processed_data(self, processed_data, output_file_path: str):
        """将处理后的数据保存为JSON"""
        if output_file_path is not None and len(output_file_path) > 0:
            # 写入文件
            with open(output_file_path, "w", encoding="utf-8") as f:
                json.dump(processed_data, f, indent=4, ensure_ascii=False)
            logger.debug(f"Data saved to {output_file_path}")
        else:
            # 打印到控制台
            print(json.dumps(processed_data, indent=4, ensure_ascii=False))


class ExcelExporter:
    """
    Excel文件写入器，将FAQ JSON数据转换为Excel文件。
    注意这里写回的excel文件并非ExcelExporter使用的excel表（该表是个N合1符合excel表），而是单独为json结构生成独立的excel表。
    主要供faq向量数据库加载使用。
    """

    def __init__(self, faq_data: list, json_file_path: Optional[str] = None):
        """
        初始化写入器。

        Args:
            faq_data: FAQ JSON数据
            json_file_path: JSON 文件的路径。
        """
        if faq_data:
            self.faq_data = faq_data
        elif json_file_path:
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    self.faq_data = json.load(f)
                print(f"成功加载 JSON 文件: {json_file_path}")
            except FileNotFoundError:
                print(f"错误: 文件未找到 {json_file_path}")
                raise
            except Exception as e:
                print(f"加载 JSON 文件时出错: {e}")
                raise e
        else:
            raise ValueError("faq_data 或 json_file_path 必须提供一个")

    def _extract_rows_from_node(self, node, parent_desc_path=None):
        """
        从节点中提取行数据。

        Args:
            node: 当前节点
            parent_desc_path: 父节点的描述路径列表

        Returns:
            包含行数据的列表
        """
        rows = []

        # 构建当前节点的描述路径
        current_desc_path = (parent_desc_path or []) + [node.get("category_desc", "")]

        # 如果当前节点有candidates，则为每个candidate添加一行
        if "candidates" in node and isinstance(node["candidates"], list):
            for candidate in node["candidates"]:
                row = {
                    "key_path": candidate.get("key_path", ""),
                    "category_desc_path": " <<< ".join(current_desc_path),
                    "answer": candidate.get("answer", ""),
                    "question_example": candidate.get("question_example", "")
                }
                rows.append(row)

        # 递归处理子分类
        if "sub_category" in node and isinstance(node["sub_category"], list):
            for sub_node in node["sub_category"]:
                rows.extend(self._extract_rows_from_node(sub_node, current_desc_path))

        return rows

    def write_excel(self, output_file_path: str):
        """
        将 JSON 数据写入 Excel 文件。

        Args:
            output_file_path: 输出的 Excel 文件路径
        """
        if self.faq_data is None:
            print("错误: 请先调用 load_json() 加载数据")
            return False

        try:
            # 提取所有行数据
            all_rows = []
            for root_node in self.faq_data:
                all_rows.extend(self._extract_rows_from_node(root_node))

            if not all_rows:
                print("警告: 没有找到包含candidates的节点")
                return False

            # 创建DataFrame
            df = pd.DataFrame(all_rows)

            # 确保输出目录存在
            output_dir = os.path.dirname(output_file_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 写入Excel文件
            df.to_excel(output_file_path, index=False, engine='openpyxl')

            print(f"成功写入 Excel 文件: {output_file_path}")
            print(f"共写入 {len(all_rows)} 行数据")
            return True

        except Exception as e:
            print(f"写入 Excel 文件时出错: {e}")
            return False


def read_excel_to_records(excel_path: str, sheet_name: str = 0) -> List[Dict[str, Any]]:
    """
    简化的Excel读取函数，直接返回记录列表

    Args:
        excel_path: Excel文件路径
        sheet_name: 工作表名称或索引

    Returns:
        记录列表，每个记录包含category_levels、answer、question_example字段
    """
    try:
        # 读取Excel数据
        data = pd.read_excel(excel_path, sheet_name=sheet_name)

        records = []
        for _, row in data.iterrows():
            # 提取分类列（前5列）
            categories = []
            for i in range(5):
                if i < len(row) and pd.notna(row.iloc[i]):
                    categories.append(str(row.iloc[i]).strip())

            # 跳过没有分类的行
            if not categories:
                continue

            # 提取答案和问题示例
            answer = ""
            if len(row) > 5 and pd.notna(row.iloc[5]):
                answer = str(row.iloc[5]).strip()

            question_example = ""
            if len(row) > 6 and pd.notna(row.iloc[6]):
                question_example = str(row.iloc[6]).strip()

            # 只保留有答案的记录
            if answer:
                records.append({
                    "category_levels": categories,
                    "answer": answer,
                    "question_example": question_example
                })

        return records

    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return []


def write_records_to_excel(
    records: List[Dict[str, Any]],
    excel_path: str,
    sheet_name: str = None
) -> bool:
    """
    简化的Excel写入函数，将记录列表写入Excel

    Args:
        records: 记录列表
        excel_path: 输出Excel文件路径
        sheet_name: 工作表名称

    Returns:
        是否写入成功
    """
    try:
        rows = []
        for record in records:
            row = {}

            # 处理分类列（最多5列）
            categories = record.get("category_levels", [])
            for i in range(5):
                col_name = f"分类{i+1}"
                if i < len(categories):
                    row[col_name] = categories[i]
                else:
                    row[col_name] = ""

            # 添加答案和问题示例
            row["答案"] = record.get("answer", "")
            row["问题示例"] = record.get("question_example", "")

            rows.append(row)

        # 创建DataFrame并写入Excel
        df = pd.DataFrame(rows)

        # 确保输出目录存在
        output_dir = os.path.dirname(excel_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)

        df.to_excel(excel_path, sheet_name=sheet_name or DEFAULT_SHEET_NAME, index=False, engine='openpyxl')

        print(f"成功写入 {len(records)} 条记录到 {excel_path}")
        return True

    except Exception as e:
        print(f"写入Excel文件失败: {e}")
        return False


def read_excel_file(excel_path: str, sheet_name: str = None) -> pd.DataFrame:
    """
    读取Excel文件
    
    Args:
        excel_path: Excel文件路径
        sheet_name: 工作表名称，默认为第一个工作表
        
    Returns:
        DataFrame对象
        
    Raises:
        FileNotFoundError: 文件不存在
        Exception: 读取失败
    """
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"Excel file not found: {excel_path}")
    
    if sheet_name is None:
        sheet_name = DEFAULT_SHEET_NAME
    
    return pd.read_excel(excel_path, sheet_name=sheet_name)


def write_dataframe_to_excel(
    data: pd.DataFrame, 
    excel_path: str, 
    sheet_name: str = None,
    preserve_other_sheets: bool = True
) -> None:
    """
    将DataFrame写入Excel文件
    
    Args:
        data: 要写入的DataFrame
        excel_path: Excel文件路径
        sheet_name: 工作表名称
        preserve_other_sheets: 是否保留其他工作表
        
    Raises:
        Exception: 写入失败
    """
    target_sheet = sheet_name or DEFAULT_SHEET_NAME
    
    # 读取现有工作表（如果需要保留）
    existing_sheets = {}
    if preserve_other_sheets and os.path.exists(excel_path):
        try:
            with pd.ExcelFile(excel_path) as excel_file:
                for sheet in excel_file.sheet_names:
                    if sheet != target_sheet:
                        existing_sheets[sheet] = pd.read_excel(excel_path, sheet_name=sheet)
        except Exception as e:
            logger.warning(f"Could not read existing sheets: {e}")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(excel_path), exist_ok=True)
    
    # 写入所有工作表
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 写入目标工作表
        data.to_excel(writer, sheet_name=target_sheet, index=False)
        
        # 写入其他现有工作表
        for sheet, sheet_data in existing_sheets.items():
            sheet_data.to_excel(writer, sheet_name=sheet, index=False)


def create_empty_excel_file(excel_path: str, sheet_name: str = None, columns: List[str] = None) -> None:
    """
    创建空的Excel文件
    
    Args:
        excel_path: Excel文件路径
        sheet_name: 工作表名称
        columns: 列名列表
        
    Raises:
        Exception: 创建失败
    """
    target_sheet = sheet_name or DEFAULT_SHEET_NAME
    default_columns = columns or ["一级类别", "二级类别", "三级类别", "四级类别", "五级类别", "答复", "问题示例"]
    
    empty_df = pd.DataFrame(columns=default_columns)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(excel_path), exist_ok=True)
    
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        empty_df.to_excel(writer, sheet_name=target_sheet, index=False)


def get_excel_sheet_names(excel_path: str) -> List[str]:
    """
    获取Excel文件的所有工作表名称
    
    Args:
        excel_path: Excel文件路径
        
    Returns:
        工作表名称列表
        
    Raises:
        FileNotFoundError: 文件不存在
        Exception: 读取失败
    """
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"Excel file not found: {excel_path}")
    
    with pd.ExcelFile(excel_path) as excel_file:
        return excel_file.sheet_names

def create_dataframe_from_records(records: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    从记录列表创建DataFrame
    
    Args:
        records: 记录列表
        
    Returns:
        DataFrame对象
    """
    return pd.DataFrame(records)


def append_dataframe_rows(existing_df: pd.DataFrame, new_rows: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    向现有DataFrame追加新行
    
    Args:
        existing_df: 现有DataFrame
        new_rows: 新行数据列表
        
    Returns:
        合并后的DataFrame
    """
    if not new_rows:
        return existing_df
    
    new_df = pd.DataFrame(new_rows)
    return pd.concat([existing_df, new_df], ignore_index=True)


def append_single_row(existing_df: pd.DataFrame, new_row: Dict[str, Any]) -> pd.DataFrame:
    """
    向现有DataFrame追加单行数据
    
    Args:
        existing_df: 现有DataFrame
        new_row: 新行数据
        
    Returns:
        合并后的DataFrame
    """
    new_row_df = pd.DataFrame([new_row])
    return pd.concat([existing_df, new_row_df], ignore_index=True)


def get_excel_file_info(excel_path: str) -> Dict[str, Any]:
    """
    获取Excel文件的详细信息
    
    Args:
        excel_path: Excel文件路径
        
    Returns:
        Excel文件信息字典
    """
    try:
        if not os.path.exists(excel_path):
            return {
                "exists": False,
                "path": excel_path
            }
        
        with pd.ExcelFile(excel_path) as excel_file:
            sheets_info = {}
            
            for sheet_name in excel_file.sheet_names:
                try:
                    sheet_data = read_excel_file(excel_path, sheet_name=sheet_name)
                    sheets_info[sheet_name] = {
                        "rows": len(sheet_data),
                        "columns": len(sheet_data.columns),
                        "column_names": list(sheet_data.columns)
                    }
                except Exception as e:
                    sheets_info[sheet_name] = {"error": str(e)}
        
        file_stat = os.stat(excel_path)
        
        return {
            "exists": True,
            "path": excel_path,
            "size_bytes": file_stat.st_size,
            "modified_time": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
            "sheets": sheets_info
        }
        
    except Exception as e:
        logger.error(f"Error getting Excel info: {e}")
        return {
            "exists": False,
            "path": excel_path,
            "error": str(e)
        }
