.navigation {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.nav-title {
  font-size: 1.25em;
  font-weight: 600;
  color: #007bff;
  margin: 0;
}

.nav-links {
  display: flex;
  gap: 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  text-decoration: none;
  color: #555;
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  transition: all 0.2s ease;
  font-size: 0.95em;
  font-weight: 500;
}

.nav-link:last-child {
  border-right: none;
}

.nav-link:hover {
  background: #e9ecef;
  color: #333;
}

.nav-link.active {
  background: #007bff;
  color: white;
}

.nav-link.active:hover {
  background: #0056b3;
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 15px;
    height: 56px;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
  }
  
  .nav-title {
    font-size: 1.1em;
  }
  
  .nav-links {
    font-size: 0.9em;
  }
  
  .nav-link {
    padding: 8px 16px;
  }
} 