import React from 'react';

/**
 * 质量检查结果展示组件
 * @param {Object} props
 * @param {Object} props.qualityCheck - 质量检查结果对象
 */
function QualityCheckResults({ qualityCheck }) {
  
  if (!qualityCheck) {
    return (
      <div className="quality-check-results empty">
        <div className="empty-state">
          <div className="empty-icon">🔍</div>
          <p>暂无质量检查结果</p>
          <small>AI分析完成后将显示质量检查信息</small>
        </div>
      </div>
    );
  }

  const { is_valid, warnings = [], errors = [], duplicate_questions = [] } = qualityCheck;

  // 计算总体状态
  const hasErrors = errors.length > 0;
  const hasWarnings = warnings.length > 0;
  const hasDuplicates = duplicate_questions.length > 0;

  // 获取整体状态
  const getOverallStatus = () => {
    if (!is_valid) return 'error';
    if (hasWarnings) return 'warning';
    return 'success';
  };

  const overallStatus = getOverallStatus();

  return (
    <div className={`quality-check-results ${overallStatus}`}>
      {/* 总体状态 */}
      <div className={`overall-status ${overallStatus}`}>
        <div className="status-icon">
          {overallStatus === 'success' && '✅'}
          {overallStatus === 'warning' && '⚠️'}
          {overallStatus === 'error' && '❌'}
        </div>
        <div className="status-text">
          <h3>
            {overallStatus === 'success' && '质量检查通过'}
            {overallStatus === 'warning' && '发现潜在问题'}
            {overallStatus === 'error' && '存在错误，需要修正'}
          </h3>
          <p>
            {overallStatus === 'success' && '您的FAQ内容符合质量标准，可以提交录入'}
            {overallStatus === 'warning' && '发现一些需要注意的问题，建议检查后再提交'}
            {overallStatus === 'error' && '发现严重问题，请修正后再次分析'}
          </p>
        </div>
      </div>

      {/* 错误信息 */}
      {hasErrors && (
        <div className="check-section errors">
          <div className="section-header">
            <span className="section-icon">❌</span>
            <h4>错误 ({errors.length})</h4>
          </div>
          <div className="issues-list">
            {errors.map((error, index) => (
              <div key={index} className="issue-item error">
                <div className="issue-icon">🚫</div>
                <div className="issue-content">
                  <p className="issue-text">{error}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 警告信息 */}
      {hasWarnings && (
        <div className="check-section warnings">
          <div className="section-header">
            <span className="section-icon">⚠️</span>
            <h4>警告 ({warnings.length})</h4>
          </div>
          <div className="issues-list">
            {warnings.map((warning, index) => (
              <div key={index} className="issue-item warning">
                <div className="issue-icon">⚠️</div>
                <div className="issue-content">
                  <p className="issue-text">{warning}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 重复问题检测 */}
      {hasDuplicates && (
        <div className="check-section duplicates">
          <div className="section-header">
            <span className="section-icon">🔄</span>
            <h4>重复问题检测 ({duplicate_questions.length})</h4>
          </div>
          <div className="issues-list">
            {duplicate_questions.map((duplicate, index) => (
              <div key={index} className="issue-item duplicate">
                <div className="issue-icon">🔄</div>
                <div className="issue-content">
                  <div className="duplicate-info">
                    <p className="duplicate-question">
                      <strong>相似问题:</strong> {duplicate.existing_question}
                    </p>
                    <p className="duplicate-category">
                      <strong>现有分类:</strong> {duplicate.existing_category?.join(' › ') || '未分类'}
                    </p>
                    <p className="similarity-score">
                      <strong>相似度:</strong> {Math.round((duplicate.similarity || 0) * 100)}%
                    </p>
                  </div>
                  {duplicate.suggestion && (
                    <div className="duplicate-suggestion">
                      <span className="suggestion-label">建议:</span>
                      <span className="suggestion-text">{duplicate.suggestion}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}



      {/* 质量检查总结 */}
      <div className="quality-summary">
        <div className="summary-stats">
          <div className="stat-item">
            <span className="stat-label">检查项目:</span>
            <span className="summary-details">
                {[
                  hasErrors ? `${errors.length}个错误` : null,
                  hasWarnings ? `${warnings.length}个警告` : null,
                  hasDuplicates ? `${duplicate_questions.length}个重复` : null
                ].filter(Boolean).join(', ') || '全部通过'}
              </span>
          </div>
        </div>

        <div className="summary-actions">
          {overallStatus === 'error' && (
            <div className="action-recommendation error">
              <strong>建议操作:</strong> 请根据错误提示修改问答内容，然后重新进行AI分析
            </div>
          )}
          {overallStatus === 'warning' && (
            <div className="action-recommendation warning">
              <strong>建议操作:</strong> 可以选择修改内容重新分析，或者使用"强制提交"忽略警告
            </div>
          )}
          {overallStatus === 'success' && (
            <div className="action-recommendation success">
              <strong>状态良好:</strong> 内容质量符合标准，可以放心提交
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default QualityCheckResults;