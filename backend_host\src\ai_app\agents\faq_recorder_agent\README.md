# AI Agent - 游戏客服 FAQ 录入器

> **路径说明**：本文档中的路径均使用基于项目根目录的绝对路径格式 `<PROJECT_ROOT>/...`，其中 `<PROJECT_ROOT>` 表示项目根目录。

## 概述

本模块实现了一个基于FastAPI的AI聊天网页Demo项目的核心组件，作为多AI平台(火山、百炼、Google)的API代理，实现了完整的FAQ录入Agent。核心包含问答分析→分类推荐→质量检查→数据写入的完整AI工作流，使用Python 3.11+uv包管理，支持模块化LLM实现和Docker部署。

该模块设计为可被上层服务（如 `<PROJECT_ROOT>/backend_host/src/ai_app/server/`）集成使用，同时也支持独立的测试和开发。支持多渠道FAQ数据管理，具备混合推荐策略和质量控制机制。

## 核心工作流

1.  **输入**: 接收用户提供的问答对 (`qa_pair`) 以及可选的渠道信息 (`channel`)。
2.  **问答分析 (QA Analysis)**: 使用LLM分析问答内容，提取主题领域、关键词等特征。
3.  **分类推荐 (Category Recommendation)**: 结合规则匹配和LLM推荐，生成合适的分类路径建议，支持新分类创建。
4.  **质量检查 (Quality Check)**: 执行重复检测、格式验证和内容质量评估。
5.  **数据写入 (Data Writing)**: 将通过检查的数据追加到Excel文件，支持备份和事务性操作。
6.  **输出**: 返回分析结果、推荐分类、质量报告或写入确认。

## 主要组件

### 核心模块

*   **`agent.py` (`FAQRecorderAgent`)**: Agent 的主入口和协调器，负责编排整个工作流程。
*   **`llm_clients.py`**: 封装LLM客户端，包括 `FAQExamineClient`、`FAQCategoryRecommendClient`、`FAQContentEnhancementClient`。
*   **`rule_based_recommender.py` (`FAQRuleBasedRecommender`)**: 基于规则和相似度的分类推荐器。
*   **`quality_checker.py` (`FAQQualityChecker`)**: 数据质量检测和验证。
*   **`excel_writer.py` (`FAQExcelWriter`)**: Excel文件操作和数据写入，支持备份。

### LLM实现层

*   **`<PROJECT_ROOT>/backend_host/src/ai_app/agents/shared/llm_impl/`**: 多平台LLM实现（共享组件）
    *   `volcano_impl.py`: 火山引擎LLM实现
    *   `bailian_impl.py`: 百炼平台LLM实现
    *   `google_impl.py`: Google LLM实现

### 数据和配置

*   **`<PROJECT_ROOT>/backend_host/src/ai_app/agents/shared/prompts/`**: 存放用于指导 LLM 的 Prompt 模板文件（共享组件）。
    *   `faq_recorder/analysis_system_prompt.md`: 问答分析系统提示。
    *   `faq_recorder/analysis_user_prompt.md`: 问答分析用户提示模板。
    *   `faq_recorder/category_recommend_system_prompt.md`: 分类推荐系统提示。
    *   `faq_recorder/category_recommend_user_prompt.md`: 分类推荐用户提示模板。
*   **`<PROJECT_ROOT>/backend_host/resources/faq_data/`**: FAQ数据文件。
    *   `faq.xlsx`: Excel格式FAQ数据，支持多sheet（通用数据表+渠道增量表）。
    *   `backups/`: 数据修改时的自动备份目录。

### 测试模块

*   **`<PROJECT_ROOT>/backend_host/tests/agents/`**: 测试文件（统一测试目录）
    *   `faq_recorder_agent/test_faq_recorder.py`: 测试问答录入器的主要功能。

## 配置

系统通过 `<PROJECT_ROOT>/backend_host/src/ai_app/config.py` 进行配置管理，支持以下配置项：

### LLM平台配置
*   **火山引擎**: `volcano_api_key`, `volcano_api_base`, `volcano_model`
*   **百炼平台**: `bailian_api_key`, `bailian_api_base`, `bailian_model`
*   **Google**: `google_api_key`, `google_api_base`, `google_model`

### 文件路径配置
*   **FAQ数据**: `faq_excel_path`
*   **Prompt模板**: `faq_analysis_system_prompt_path`, `faq_analysis_user_prompt_path` 等

配置可通过环境变量或配置文件设置，支持多渠道FAQ文件自动切换。

## 使用

### 基本使用

上层服务（如 `<PROJECT_ROOT>/backend_host/src/ai_app/server/`）可以通过导入 `FAQRecorderAgent` 类并调用其方法来使用此模块：

```python
from ai_app.agents.faq_recorder_agent.agent import FAQRecorderAgent
from ai_app.models.faq_recorder import FAQRecorderAnalyzeRequest, FAQRecorderQuestionAnswer

# 初始化Agent
agent = FAQRecorderAgent(model_platform='volcano', channel='zulong')

# 分析问答
request = FAQRecorderAnalyzeRequest(
    qa_pair=FAQRecorderQuestionAnswer(
        question="用户忘记密码怎么办",
        answer="请联系客服重置密码"
    )
)
result = await agent.analyze_qa_pair(request)
```

### API端点

- `POST /api/v1/faq-recorder/analyze` - 分析问答内容并推荐分类
- `POST /api/v1/faq-recorder/categories` - 获取分类结构
- `POST /api/v1/faq-recorder/submit` - 提交数据到Excel

## 测试

从项目根目录执行：

```bash
cd <PROJECT_ROOT>/backend_host

# 运行所有测试
python -m tests.agents.faq_recorder_agent.test_faq_recorder all --platform volcano

# 测试问答分析
python -m tests.agents.faq_recorder_agent.test_faq_recorder analyze --platform volcano

# 测试分类结构
python -m tests.agents.faq_recorder_agent.test_faq_recorder categories

# 测试录入提交
python -m tests.agents.faq_recorder_agent.test_faq_recorder submit

# 测试独立组件
python -m tests.agents.faq_recorder_agent.test_faq_recorder components
```

## 功能特性

✅ **多平台LLM支持**: 火山、百炼、Google
✅ **混合推荐**: 规则匹配 + LLM推荐
✅ **多渠道FAQ**: 支持不同渠道的数据管理
✅ **质量控制**: 重复检测、格式验证、内容评估
✅ **事务写入**: 支持备份和回滚
✅ **完整工作流**: 分析→推荐→检查→写入
✅ **错误处理**: 优雅降级和详细日志记录

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────┐
│              FAQRecorderAgent           │  ← 主协调器
├─────────────────────────────────────────┤
│  Analysis     │ Recommend  │ Quality    │  ← 业务逻辑层
│  Client       │ Client     │ Checker    │
├─────────────────────────────────────────┤
│  VolcanoLLM   │ BailianLLM │ GoogleLLM  │  ← LLM实现层
│  Impl         │ Impl       │ Impl       │
├─────────────────────────────────────────┤
│  RuleBased    │ ExcelWriter             │  ← 数据处理层
│  Recommender  │                         │
└─────────────────────────────────────────┘
```

### 数据流

```
用户输入 → 问答分析 → 分类推荐 ┬→ 质量检查 → 数据写入 → 返回结果
                           ↓
                      质量失败检测
                           ↓
                      返回警告/错误
```

## 性能优化

- **缓存机制**: FAQ结构解析结果缓存
- **批量操作**: 支持批量写入
- **相似度计算**: 使用高效的QuickMatcher

## 错误处理

### 分级处理
1. **致命错误**: 配置缺失、文件不存在 → 抛出异常
2. **业务错误**: LLM调用失败 → 降级到规则匹配
3. **降级处理**: 质量检查失败 → 返回警告继续
4. **保底机制**: 无推荐时建议新分类

### 日志记录
- **INFO**: 正常流程和关键节点
- **WARNING**: 非致命错误和降级处理
- **ERROR**: 业务错误和异常情况
- **DEBUG**: 详细的调试信息

## 扩展指南

### 添加新的LLM平台
1. 在 `<PROJECT_ROOT>/backend_host/src/ai_app/agents/shared/llm_impl/` 下创建新的实现类
2. 继承 `BaseLLMImpl`
3. 在 `<PROJECT_ROOT>/backend_host/src/ai_app/config.py` 中添加配置项
4. 在 `agent.py` 中添加初始化逻辑

### 添加新的质量检查规则
1. 扩展 `quality_checker.py` 的 `_check_format_validity` 或 `_assess_content_quality`
2. 添加新的验证逻辑和阈值

### 自定义推荐策略
1. 扩展 `rule_based_recommender.py` 的匹配方法
2. 调整相似度阈值和合并逻辑