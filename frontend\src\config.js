// 集中管理前端配置
// 支持三种配置方式：运行时配置（Docker）、.env文件（开发）、默认值

const getConfig = () => {
  if (typeof window !== 'undefined' && window.APP_CONFIG) {
    // 优先使用运行时配置（Docker环境）
    console.log('Using runtime config:', window.APP_CONFIG);
    return window.APP_CONFIG
  } else {
    // 开发环境：使用.env文件中的REACT_APP_*环境变量
    // React会自动加载.env文件中以REACT_APP_开头的变量
    const envConfig = {
      API_BASE_URL: process.env.REACT_APP_API_BASE_URL || "http://localhost:8000/api/v1",
    };
    console.log('Using .env config:', envConfig)
    return envConfig
  }
};

const CONFIG = getConfig();

export default CONFIG;