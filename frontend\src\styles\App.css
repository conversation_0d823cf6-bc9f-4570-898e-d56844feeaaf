/* 全局应用样式 */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f3f6fa;
  font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.app-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

/* 聊天容器样式 - 适配新布局 */
.chat-container {
  width: 66vw;
  max-width: 90vw;
  height: calc(66vh);
  max-height: calc(90vh - 64px); /* 减去导航栏高度 */
  border: none;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08), 0 1.5px 4px rgba(0,0,0,0.03);
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow: hidden;
}

/* 移动端适配 */
@media (max-width: 500px) {
  .app-content {
    padding: 0;
  }
  
  .chat-container {
    width: 100vw;
    height: calc(100vh - 56px); /* 移动端导航栏高度 */
    border-radius: 0;
    box-shadow: none;
    max-height: none;
  }
} 