import React from 'react';

/**
 * AI推荐结果展示组件
 * @param {Object} props
 * @param {Array} props.recommendations - AI推荐的分类列表
 * @param {Function} props.onSelect - 选择推荐分类的回调
 * @param {Array} props.selectedPath - 当前选中的分类路径
 */
function AIRecommendations({ recommendations, onSelect, selectedPath }) {
  
  // 检查路径是否被选中
  const isPathSelected = (path) => {
    if (!selectedPath || selectedPath.length === 0) return false;
    return JSON.stringify(path) === JSON.stringify(selectedPath);
  };

  // 获取置信度等级
  const getConfidenceLevel = (confidence) => {
    if (confidence >= 0.9) return 'high';
    if (confidence >= 0.7) return 'medium';
    return 'low';
  };

  // 获取置信度文本
  const getConfidenceText = (confidence) => {
    const percentage = Math.round(confidence * 100);
    return `${percentage}%`;
  };

  if (!recommendations || recommendations.length === 0) {
    return (
      <div className="ai-recommendations empty">
        <div className="empty-state">
          <div className="empty-icon">🤖</div>
          <p>暂无AI推荐结果</p>
          <small>请先填写问答内容并点击"开始AI分析"</small>
        </div>
      </div>
    );
  }

  return (
    <div className="ai-recommendations">
      <div className="recommendations-header">
        <h3>🎯 AI智能推荐</h3>
        <p className="header-description">
          基于问答内容分析，为您推荐最合适的分类路径
        </p>
      </div>

      <div className="recommendations-list">
        {recommendations.map((recommendation, index) => (
          <div
            key={index}
            className={`recommendation-item ${getConfidenceLevel(recommendation.confidence)} ${
              isPathSelected(recommendation.category_path) ? 'selected' : ''
            }`}
          >
            <div className="recommendation-header">
              <div className="recommendation-rank">
                <span className="rank-number">#{index + 1}</span>
                <div className={`confidence-badge ${getConfidenceLevel(recommendation.confidence)}`}>
                  <span className="confidence-text">
                    {getConfidenceText(recommendation.confidence)}
                  </span>
                  <div className="confidence-bar">
                    <div 
                      className="confidence-fill" 
                      style={{width: `${recommendation.confidence * 100}%`}}
                    ></div>
                  </div>
                </div>
              </div>
              
              <button
                className={`select-button ${isPathSelected(recommendation.category_path) ? 'selected' : ''}`}
                onClick={() => onSelect(recommendation.category_path)}
              >
                {isPathSelected(recommendation.category_path) ? '✓ 已选择' : '选择此分类'}
              </button>
            </div>

            <div className="recommendation-content">
              <div className="category-path">
                <div className="path-breadcrumb">
                  {recommendation.category_path.map((category, catIndex) => (
                    <React.Fragment key={catIndex}>
                      <span className="path-item">{category}</span>
                      {catIndex < recommendation.category_path.length - 1 && (
                        <span className="path-separator">›</span>
                      )}
                    </React.Fragment>
                  ))}
                </div>
                
                {recommendation.is_new_category && (
                  <span className="new-category-badge">
                    ✨ 新建议分类
                  </span>
                )}
              </div>

              <div className="recommendation-reason">
                <span className="reason-label">推荐理由：</span>
                <span className="reason-text">{recommendation.reason}</span>
              </div>

              {recommendation.similar_existing && recommendation.similar_existing.length > 0 && (
                <div className="recommendation-reason">
                  <span className="reason-label">相似分类：</span>
                  <span className="reason-text">{recommendation.similar_existing.join(' > ')}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="recommendations-footer">
        <div className="confidence-legend">
          <span className="legend-title">置信度说明：</span>
          <div className="legend-items">
            <span className="legend-item high">
              <span className="legend-color"></span>
              高 (90%+)
            </span>
            <span className="legend-item medium">
              <span className="legend-color"></span>
              中 (70-90%)
            </span>
            <span className="legend-item low">
              <span className="legend-color"></span>
              低 (&lt;70%)
            </span>
          </div>
        </div>
        
        <div className="recommendations-tips">
          <small>
            💡 您可以直接选择AI推荐的分类，也可以在下方手动选择或添加新分类
          </small>
        </div>
      </div>
    </div>
  );
}

export default AIRecommendations;