import os
import sys
import argparse
import logging

# 配置日志记录
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 当前脚本路径: <backend_host_dir>/tools/gen_knowledge_lib.py
backend_host_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

from ai_app.config import get_logging_config
from ai_app.services.faq_management.faq_repo_manager import FAQRepoManager
from ai_app.shared.excel_utils import ExcelExporter

def generate_knowledge_library(input_excel_path: str, output_dir: str):
    """
    从源FAQ Excel文件加载数据，并为每个channel生成独立的知识库Excel文件。

    Args:
        input_excel_path (str): 原始FAQ Excel文件的路径。
        output_dir (str): 生成的知识库文件存放的目录。
    """
    logger.info(f"开始处理FAQ文件: {input_excel_path}")

    input_excel_name, input_excel_ext = os.path.basename(input_excel_path).split('.')
    logger.debug(f"输入Excel文件名: {input_excel_name}, 扩展名: {input_excel_ext}")

    # 1. 初始化并加载FAQ数据
    try:
        faq_manager = FAQRepoManager(excel_path=input_excel_path)
        if not faq_manager.load_from_excel():
            logger.error("加载FAQ数据失败，请检查Excel文件和路径。")
            return
    except Exception as e:
        logger.error(f"初始化FAQ管理器时发生错误: {e}")
        return

    # 2. 准备输出目录
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            logger.info(f"已创建输出目录: {output_dir}")
        except OSError as e:
            logger.error(f"创建输出目录 {output_dir} 失败: {e}")
            return

    # 3. 获取所有channels (包括 'common')
    channels = faq_manager.faq_data.keys()
    logger.debug(f"找到以下数据表: {channels}")

    # 4. 遍历每个channel并导出为Excel
    for channel in channels:
        try:
            # 获取对应channel的数据
            faq_data, _ = faq_manager.get_faq_data(channel)
            if not faq_data:
                logger.warning(f"Channel '{channel}' 数据为空，跳过导出。")
                continue

            # 使用ExcelExporter导出
            exporter = ExcelExporter(faq_data=faq_data)
            
            # 构建输出文件名
            output_filename = f"{input_excel_name}-{channel}.{input_excel_ext}"
            output_filepath = os.path.join(output_dir, output_filename)
            
            logger.debug(f"正在将 channel '{channel}' 的数据导出到: {output_filepath}")
            success = exporter.write_excel(output_file_path=output_filepath)
            if not success:
                logger.error(f"导出 channel '{channel}' 失败。")

        except Exception as e:
            logger.error(f"处理 channel '{channel}' 时发生意外错误: {e}")

    logger.info("所有channel处理完毕。")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="从FAQ Excel源文件生成独立的知识库Excel文件。")
    
    # 设置默认输入和输出路径
    default_input = os.path.join(backend_host_dir, 'resources', 'faq_data', 'faq.xlsx')
    default_output = os.path.join(backend_host_dir, 'resources', 'faq_data', 'knowledge_lib')

    parser.add_argument(
        "--input",
        type=str,
        default=default_input,
        help=f"输入的FAQ Excel文件路径。默认为: {default_input}"
    )
    parser.add_argument(
        "--output",
        type=str,
        default=default_output,
        help=f"输出的知识库目录路径。默认为: {default_output}"
    )
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable DEBUG level logging.")

    args = parser.parse_args()

    # 根据命令行参数配置日志
    logging.config.dictConfig(get_logging_config(cli_verbose_flag=args.verbose))


    # 确保路径是绝对路径
    input_path = os.path.abspath(args.input)
    output_path = os.path.abspath(args.output)

    if not os.path.exists(input_path):
        print(f"错误: 输入文件不存在: {input_path}")
        sys.exit(1)

    generate_knowledge_library(input_path, output_path)