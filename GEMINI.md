# AI智能客服系统项目分析总结

## 项目概述

这是一个企业级AI智能客服系统，集成了多AI平台支持、FAQ筛选代理、FAQ录入代理、批量测试等完整功能。系统支持中英双语，采用前后端分离的微服务架构设计，可作为多个AI平台（百炼、Coze、火山方舟、Google等）的统一代理服务。

- **前端 (`frontend/`)**: 基于 **React 19** 的现代化单页应用，提供智能对话、批量测试、FAQ录入等完整用户界面。支持多平台切换、实时反馈、响应式设计。
- **后端 (`backend_host/`)**: 基于 **FastAPI** 的高性能API服务，作为多AI平台的代理和编排层，实现了完整的FAQ筛选工作流和智能FAQ录入功能。
- **部署 (`docker/`)**: 支持 **Docker** 和 **Docker Compose** 的容器化部署，前后端分离，支持多环境部署。

## 核心业务逻辑：双Agent架构

后端采用双Agent架构，包含两个核心AI代理：

### 1. FAQ筛选器Agent (`faq_filter_agent/`)

实现了完整的FAQ筛选工作流程，旨在从知识库中为用户问题找到最精确的答案：

1.  **查询重写 (Query Rewrite)**: 分析用户的多轮对话历史，将用户最新的、可能不完整的提问，重写成一个清晰、独立的查询语句。
2.  **问题分类 (Classification)**: 利用大语言模型（LLM）和预设的FAQ目录（从Excel文件动态生成），对重写后的查询进行分类，找到最可能的类别。
3.  **答案检索 (Retrieval)**: 根据分类结果，从Excel格式的FAQ数据文件中精确查找并提取标准答案。
4.  **向量召回 (Fallback)**: 如果问题分类失败（如LLM未返回有效分类），系统自动切换到**向量搜索**模式，使用火山向量数据库进行语义相似度查找，作为补充机制。
5.  **重排序 (Reranking)**: （可选）对检索到的多个候选答案，使用专门的重排序模型进行打分和排序，提升最终答案的准确性。
6.  **返回结果**: 将最优的候选答案连同分类路径、置信度分数等详细信息返回给前端。

### 2. FAQ录入代理 (`faq_recorder_agent/`)

提供一个智能化的FAQ数据录入工作流，旨在简化知识库的维护过程（MVP 90%+ 完成）：

1.  **问题分析**: 使用LLM分析用户输入的原始问答内容，提取关键信息，如主题、意图等。
2.  **分类推荐**: 基于现有FAQ结构和LLM的理解能力，智能推荐最合适的分类路径。
3.  **重复检测**: 通过相似度计算，检测新增问答与知识库中现有条目的重复和冲突。
4.  **质量评估**: 评估答案的完整性、清晰度和格式规范性。
5.  **数据录入**: 将经过审核确认的问答数据安全地写入后端的Excel知识库文件中，并自动创建备份。

## 技术栈总结

-   **前端**:
    -   框架: **React 19.1.0**
    -   路由: **React Router 7.6.2**
    -   HTTP客户端: **Axios**
    -   构建工具: **Create React App**
    -   包管理器: **npm**
    -   关键库: **XLSX**, **React Dropzone**, **String Similarity**

-   **后端**:
    -   框架: **FastAPI**
    -   ASGI服务器: **Uvicorn**
    -   包和环境管理: **uv**
    -   语言: **Python 3.11**
    -   核心库: **Pydantic**, **httpx**, **Pandas**, **FuzzyWuzzy**, **VolcEngine**, **Jieba**

-   **AI/LLM集成**:
    -   支持平台: **百炼 (Bailian), Coze, 火山引擎 (Volcano), Google**
    -   核心能力: 文本生成、向量嵌入、语义检索、重排序
    -   向量数据库: **火山知识库**

-   **部署与运维**:
    -   **Docker** & **Docker Compose**
    -   Web服务器 (前端): **Nginx** (在Docker容器内)
    -   支持多环境（开发/生产）分离部署

## 项目特点与优势

### 功能特性
- **🤖 多AI平台支持**: 设计了统一的LLM调用接口，可无缝切换百炼、火山、Coze、Google等多个平台。
- **🧠 智能工作流**: 实现了复杂的、多步骤的FAQ筛选和录入AI工作流，逻辑严谨。
- **📊 数据管理**: 统一的FAQ数据仓库管理器，以Excel作为中心数据源，支持多渠道数据（通过多sheet页）合并与管理，并提供自动备份机制。
- **🔍 混合检索策略**: 以精确的LLM分类检索为主，以语义化的向量检索为辅，实现了优雅降级和召回补充。
- **📝 智能录入**: 基于LLM的辅助录入功能，显著提升知识库维护效率和数据质量。
- **🧪 批量测试**: 前端提供完整的批量测试功能，支持通过上传Excel文件对FAQ系统进行大规模自动化测试和回归验证。

### 技术架构
- **模块化设计**: 后端Agent职责清晰，LLM实现、数据处理、服务接口等分层明确，易于扩展和维护。
- **配置驱动**: 大量行为通过环境变量和配置文件进行管理，包括API密钥、模型名称、Prompt路径等，灵活性高。
- **容器化**: 提供完整的`Dockerfile`和`docker-compose.yml`，实现一键化部署和环境隔离。
- **异步处理**: 后端基于FastAPI和httpx，大量采用异步IO，性能优越。

## 项目状态

-   **核心功能**: FAQ筛选代理和FAQ录入代理均已完整实现
-   **前端**: 智能对话、批量测试、FAQ录入三大功能模块已全部完成
-   **后端**: 架构稳定，模块化程度高，支持多AI平台集成
-   **数据管理**: 基于Excel的统一数据管理方案，支持多渠道和自动备份
-   **部署**: 完善的Docker容器化部署方案