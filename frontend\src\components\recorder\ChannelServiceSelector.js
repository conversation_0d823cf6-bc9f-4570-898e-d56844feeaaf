import React, { useState, useEffect } from 'react';
import ServiceSelector from '../common/ServiceSelector';
import {ChannelSelector, channelOptions} from '../common/ChannelSelector';

/**
 * FAQ录入系统的渠道和服务选择组件
 * @param {Object} props
 * @param {string} props.channel - 当前选中的渠道
 * @param {string} props.service - 当前选中的服务
 * @param {Function} props.onChannelChange - 渠道变化回调
 * @param {Function} props.onServiceChange - 服务变化回调
 */
function ChannelServiceSelector({ channel, service, onChannelChange, onServiceChange }) {
  // 通用模式状态
  const [isCommonMode, setIsCommonMode] = useState(false);

  // 初始化通用模式状态
  useEffect(() => {
    setIsCommonMode(!channel || channel === '');
  }, [channel]);

  /**
   * 处理通用模式复选框变化
   * @param {Event} e - 复选框变化事件
   */
  const handleCommonModeChange = (e) => {
    const checked = e.target.checked;
    setIsCommonMode(checked);
    
    if (checked) {
      // 启用通用模式，清空渠道选择，触发父组件重新加载分类
      onChannelChange('');
    } else {
      // 取消通用模式，默认使用ChannelSelector的第一个渠道option
      onChannelChange(channelOptions[0].value);
    }
  };

  /**
   * 处理渠道选择变化
   * @param {string} selectedChannel - 选中的渠道
   */
  const handleChannelChange = (selectedChannel) => {
    // 先更新父组件状态，触发分类重新加载
    onChannelChange(selectedChannel);
    // 如果选择了具体渠道，自动取消通用模式
    if (selectedChannel && selectedChannel !== '') {
      setIsCommonMode(false);
    }
  };

  return (
    <div className="channel-service-selector">
      {/* 第一行：目标渠道 + 通用复选框 */}
      <div className="selector-row">
        <div className="selector-group channel-group">
          <label htmlFor="channel-select">目标渠道:</label>
          <ChannelSelector
            channelName={channel}
            onChannelChange={handleChannelChange}
            displayMode="compact"
            className={`faq-channel-selector ${isCommonMode ? 'disabled' : ''}`}
            disabled={isCommonMode}
          />
          <div className="common-checkbox-wrapper">
            <label className="common-checkbox-label">
              <input
                type="checkbox"
                className="common-checkbox"
                checked={isCommonMode}
                onChange={handleCommonModeChange}
              />
              <span className="checkbox-text">通用</span>
            </label>
          </div>
        </div>
      </div>

      {/* 第二行：AI服务平台 */}
      <div className="selector-row">
        <div className="selector-group service-group">
          <label htmlFor="service-selector">AI服务平台:</label>
          <ServiceSelector
            serviceName={service}
            onServiceChange={onServiceChange}
            displayMode="compact"
            className="faq-service-selector"
          />
        </div>
      </div>
    </div>
  );
}

export default ChannelServiceSelector;