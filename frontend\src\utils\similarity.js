import stringSimilarity from 'string-similarity';

/**
 * 计算两个字符串之间的相似度
 * @param {string} str1 - 第一个字符串
 * @param {string} str2 - 第二个字符串
 * @returns {number} - 相似度分数 (0-1之间)
 */
export const calculateSimilarity = (str1, str2) => {
  if (!str1 || !str2) {
    return 0;
  }
  
  // 使用string-similarity的compareTwoStrings方法
  return stringSimilarity.compareTwoStrings(str1.toString(), str2.toString());
};

/**
 * 将相似度转换为百分比字符串
 * @param {number} similarity - 相似度分数 (0-1之间)
 * @returns {string} - 百分比字符串，如"85%"
 */
export const formatSimilarityPercentage = (similarity) => {
  return `${Math.round(similarity * 100)}%`;
};

/**
 * 根据相似度生成颜色（红绿插值）
 * @param {number} similarity - 相似度分数 (0-1之间)
 * @returns {string} - RGB颜色字符串
 */
export const getSimilarityColor = (similarity) => {
  // 确保相似度在0-1范围内
  const clampedSimilarity = Math.max(0, Math.min(1, similarity));
  
  // 红色到绿色的插值
  // 红色: rgb(220, 53, 69)
  // 绿色: rgb(40, 167, 69)
  const red = Math.round(220 - (220 - 40) * clampedSimilarity);
  const green = Math.round(53 + (167 - 53) * clampedSimilarity);
  const blue = Math.round(69);
  
  return `rgb(${red}, ${green}, ${blue})`;
};