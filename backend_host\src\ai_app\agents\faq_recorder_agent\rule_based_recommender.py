"""
智能分类推荐器

基于现有FAQ结构和问答内容，使用AI技术推荐最合适的分类路径。
"""

import sys
import json
from typing import List, Dict, Any, Optional, Tuple
import jieba

from ai_app.models.faq_recorder import CategoryRecommendation, FAQRecorderQuestionAnswer
from ai_app.services.faq_management.data_parser import FAQDataParser
from ai_app.shared.exceptions import CategoryRecommendationError
from ai_app.shared.request_tracing import get_traced_logger
from ai_app.shared.quick_matcher import QuickMatcher

logger = get_traced_logger(__name__)


class FAQRuleBasedRecommender:
    """
    智能分类推荐器
    
    功能：
    1. 基于现有分类结构分析问答内容
    2. 推荐最合适的分类路径
    3. 计算推荐置信度和相似度
    4. 支持新分类建议
    """
    
    def __init__(self, faq_parser: FAQDataParser):
        """
        初始化推荐器
        
        Args:
            faq_parser: FAQ数据解析器实例
        """
        self.faq_parser = faq_parser
        logger.debug("FAQRuleBasedRecommender initialized")
    
    def recommend_categories(
        self, 
        qa_pair: FAQRecorderQuestionAnswer,
        max_recommendations: int = 5
    ) -> List[CategoryRecommendation]:
        """
        推荐分类路径
        
        Args:
            qa_pair: 问答对数据
            max_recommendations: 最大推荐数量
            
        Returns:
            推荐结果列表，按置信度降序排列
            
        Raises:
            CategoryRecommendationError: 推荐过程中发生错误
        """
        try:
            logger.info(f"Starting category recommendation for question: {qa_pair.question[:50]}...")
            
            # 1. 获取所有现有分类路径
            existing_categories = self.faq_parser.extract_all_category_paths()
            #logger.debug(f"Total {len(existing_categories)} existing categories found:\n{existing_categories}")
            
            # 2. 基于关键词匹配进行初步筛选
            keyword_matches = self._keyword_based_matching(qa_pair, existing_categories)
            
            # 3. 基于语义相似度进行精细匹配
            similarity_matches = self._similarity_based_matching(qa_pair, existing_categories)
            
            # 4. 合并和排序推荐结果
            recommendations = self._merge_and_rank_recommendations(
                keyword_matches, similarity_matches, max_recommendations
            )
            
            # 5. 至少一个新分类建议
            new_category_suggestions = self._suggest_new_categories(qa_pair, existing_categories)
            recommendations.extend(new_category_suggestions)
            
            # 6. 最终排序和截断
            recommendations = sorted(recommendations, key=lambda x: x.confidence, reverse=True)
            recommendations = recommendations[:max_recommendations]
            
            logger.info(f"Generated {len(recommendations)} category recommendations: {[rec.category_path for rec in recommendations]}")
            return recommendations
            
        except Exception as e:
            logger.error(f"Error during category recommendation: {e}")
            raise CategoryRecommendationError(f"Failed to recommend categories: {e}") from e
    
    def _keyword_based_matching(
        self, 
        qa_pair: FAQRecorderQuestionAnswer, 
        existing_categories: List[List[str]]
    ) -> List[CategoryRecommendation]:
        """
        基于关键词匹配进行推荐
        
        Args:
            qa_pair: 问答对
            existing_categories: 现有分类路径列表
            
        Returns:
            基于关键词的推荐结果
        """
        recommendations = []

        # 使用QuickMatcher进行关键词匹配
        quick_matcher = QuickMatcher()
        qa_text = qa_pair.question + "\n" + qa_pair.answer
        category_dict = {' '.join(category_path): category_path for category_path in existing_categories} # 分类路径字符串到分类路径的映射
        match_results = quick_matcher.find_most_similar(qa_text, category_dict.keys(), method="token_set_ratio", limit=5) # 最多返回5个结果
        
        for result in match_results:
            if result[1] > 0.1:  # 设置最低相似度阈值
                recommendation = CategoryRecommendation(
                    category_path=category_dict[result[0]],
                    confidence=result[1] * 0.8,  # 关键词匹配最高置信度限制为0.8
                    reason=f"关键词匹配: {result[0]}",
                    is_new_category=False
                )
                recommendations.append(recommendation)
        
        # 按置信度排序
        recommendations.sort(key=lambda x: x.confidence, reverse=True)
        logger.debug(f"Keyword matching generated {len(recommendations)} recommendations: {json.dumps([(rec.category_path, rec.confidence) for rec in recommendations], indent=2, ensure_ascii=False)}")
        
        return recommendations
    
    def _similarity_based_matching(
        self, 
        qa_pair: FAQRecorderQuestionAnswer, 
        existing_categories: List[List[str]]
    ) -> List[CategoryRecommendation]:
        """
        基于语义相似度进行推荐
        
        Args:
            qa_pair: 问答对
            existing_categories: 现有分类路径列表
            
        Returns:
            基于相似度的推荐结果
        """
        recommendations = []
        
        for category_path in existing_categories:
            # 计算与分类路径的文本相似度
            quick_matcher = QuickMatcher()
            category_text = " ".join(category_path).lower()
            similarity = quick_matcher.calculate_similarity(qa_pair.question.lower(), category_text)
            
            # 同时考虑问题与该分类下现有问题的相似度
            existing_questions_similarity = self._calculate_existing_questions_similarity(qa_pair.question, category_path)
            
            # 综合相似度得分
            combined_similarity = (similarity * 0.3 + existing_questions_similarity * 0.7)
            
            if combined_similarity > 0.2:  # 设置最低相似度阈值
                recommendation = CategoryRecommendation(
                    category_path=category_path,
                    confidence=min(combined_similarity, 0.9),
                    reason=f"语义相似度: {combined_similarity:.2f}",
                    is_new_category=False
                )
                recommendations.append(recommendation)
        
        # 按置信度排序
        recommendations.sort(key=lambda x: x.confidence, reverse=True)
        logger.debug(f"Similarity matching generated {len(recommendations)} recommendations: {json.dumps([(rec.category_path, rec.confidence) for rec in recommendations], indent=2, ensure_ascii=False)}")
        
        return recommendations[:5]  # 返回前5个结果
    
    def _calculate_existing_questions_similarity(
        self, 
        question: str, 
        category_path: List[str]
    ) -> float:
        """
        计算问题与指定分类下现有问题的相似度
        
        Args:
            question: 输入问题
            category_path: 分类路径
            
        Returns:
            相似度得分 (0-1)
        """
        try:
            # 将分类路径转换为key_path
            category_key_path = self.faq_parser.get_key_path_from_desc_path(category_path)
            if not category_key_path:
                return 0.0
            
            # 获取该分类下的所有答案
            answers = self.faq_parser.get_answers_by_key_path(category_key_path)
            if not answers:
                return 0.0
            
            # 计算与现有问题示例的相似度
            max_similarity = 0.0
            question_lower = question.lower()
            quick_matcher = QuickMatcher()
            
            for answer_data in answers:
                question_example = answer_data.get("question_example", "")
                if question_example:
                    similarity = quick_matcher.calculate_similarity(question_lower, question_example.lower())
                    max_similarity = max(max_similarity, similarity)
            
            return max_similarity
            
        except Exception as e:
            logger.debug(f"Error calculating existing questions similarity: {e}")
            return 0.0
    
    def _merge_and_rank_recommendations(
        self, 
        keyword_matches: List[CategoryRecommendation],
        similarity_matches: List[CategoryRecommendation],
        max_count: int
    ) -> List[CategoryRecommendation]:
        """
        合并和排序推荐结果
        
        Args:
            keyword_matches: 关键词匹配结果
            similarity_matches: 相似度匹配结果
            max_count: 最大返回数量
            
        Returns:
            合并后的推荐结果
        """
        # 使用字典避免重复，键为分类路径的字符串表示
        merged_recommendations = {}
        
        # 处理关键词匹配结果
        for rec in keyword_matches:
            path_key = " > ".join(rec.category_path)
            if path_key not in merged_recommendations:
                merged_recommendations[path_key] = rec
            else:
                # 如果已存在，取较高的置信度
                existing = merged_recommendations[path_key]
                if rec.confidence > existing.confidence:
                    merged_recommendations[path_key] = rec
        
        # 处理相似度匹配结果
        for rec in similarity_matches:
            path_key = " > ".join(rec.category_path)
            if path_key not in merged_recommendations:
                merged_recommendations[path_key] = rec
            else:
                # 如果已存在，合并置信度和原因
                existing = merged_recommendations[path_key]
                combined_confidence = (existing.confidence + rec.confidence) / 2
                combined_reason = f"{existing.reason}; {rec.reason}"
                
                merged_recommendations[path_key] = CategoryRecommendation(
                    category_path=rec.category_path,
                    confidence=min(combined_confidence, 1.0),
                    reason=combined_reason,
                    is_new_category=False
                )
        
        # 转换为列表并排序
        result = list(merged_recommendations.values())
        result.sort(key=lambda x: x.confidence, reverse=True)
        
        return result[:max_count]
    
    def _suggest_new_categories(
        self, 
        qa_pair: FAQRecorderQuestionAnswer,
        existing_categories: List[List[str]]
    ) -> List[CategoryRecommendation]:
        """
        基于问答内容建议新分类
        
        Args:
            qa_pair: 问答对
            existing_categories: 现有分类列表
            
        Returns:
            新分类建议列表
        """
        suggestions = []
        
        # 简单的新分类建议逻辑
        # 这里可以使用更复杂的NLP技术或LLM来生成新分类建议
        
        # 从问题中提取可能的分类词汇
        keywords = self._extract_keywords(qa_pair.question)
        
        # 查找最相似的现有分类作为基础
        quick_matcher = QuickMatcher()
        category_dict = {' '.join(category): category for category in existing_categories}
        best_result = quick_matcher.find_most_similar(qa_pair.question.lower(), category_dict.keys(), method="token_set_ratio", limit=1)
        if not best_result:
            return suggestions
        best_match = category_dict.get(best_result[0][0])
        if not best_match:
            return suggestions
        
        for keyword in keywords:
            # 基于最佳匹配分类和关键词建议新分类
            suggested_path = best_match.copy()
            
            # 添加一个新的子分类
            suggested_path.append(keyword)

            # 检查新分类是否已存在
            if category_dict.get(' '.join(suggested_path)):
                continue
            suggestion = CategoryRecommendation(
                category_path=suggested_path,
                confidence=best_result[0][1]*0.8,  # 新分类建议的置信度相对较低
                reason=f"基于关键词'{keyword}'的新分类建议",
                is_new_category=True,
                similar_existing=[" > ".join(best_match)]
            )
            suggestions.append(suggestion)
            logger.debug(f"Generated new category suggestions:{suggestion.category_path}, confidence={suggestion.confidence}")

            if len(suggestions) >= 1:
                # 暂时就只允许一个新分类
                break

        return suggestions
    
    def _extract_keywords(self, text: str) -> List[str]:
        """
        从文本中提取关键词
        
        Args:
            text: 输入文本
            
        Returns:
            关键词列表
        """
        # 简单的关键词提取逻辑
        # 在实际应用中，可以使用更复杂的NLP技术
        
        # 使用jieba进行中文分词
        words = jieba.cut(text)
        
        # 过滤停用词和短词
        stop_words = {'的', '了', '在', '是', '有', '和', '与', '或', '但', '如何', '什么', '怎么', '为什么', '哪里', '谁', '我', '你', '他', '她', '它', '我们', '你们', '他们'}
        keywords = []
        for word in words:
            if len(word) < 2:
                continue
            # 如果word包含任意停用词，也跳过
            if any(stop_word in word for stop_word in stop_words):
                continue
            keywords.append(word)
        
        # 去重并保持顺序
        seen = set()
        unique_keywords = []
        for keyword in keywords:
            if keyword not in seen:
                seen.add(keyword)
                unique_keywords.append(keyword)
        
        # 按从长到短排序
        unique_keywords.sort(key=len, reverse=True)

        return unique_keywords[:10]  # 返回前10个关键词
