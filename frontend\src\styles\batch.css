/* === 全局布局 === */
.batch-test-container {
  padding: 20px;
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  width: 1200px; /* 强制固定宽度而不是最大宽度 */
  margin: 0 auto; /* 水平居中 */
  box-sizing: border-box; /* 确保 padding 不会影响总宽度 */
  /* 内容区域实际可用宽度：1200px - 40px padding = 1160px */
}

.batch-header {
  text-align: center;
  margin-bottom: 24px;
}

.batch-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
}

.batch-header p {
  font-size: 1rem;
  color: #5a6876;
}

.batch-content {
  /* 使用 CSS Grid 布局替代原来的默认布局 */
  display: grid;
  grid-template-rows: auto auto 1fr; /* 配置区域、控制按钮、动态内容区域 */
  gap: 24px;
  min-height: 600px; /* 确保有足够空间 */
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  /* 性能优化：减少重排重绘 */
  contain: layout style;
  will-change: auto;
  /* 确保内容区域有明确的宽度 */
  width: 1112px; /* 1160px - 48px padding = 1112px */
}

/* === 配置区域 === */
.batch-config-area {
  /* 使用 CSS Grid 替代 Flexbox，确保固定布局 */
  display: grid;
  grid-template-columns: 544px 544px; /* 两列各544px，总宽度544+24+544=1112px，正好匹配父容器 */
  gap: 24px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 24px;
  /* 性能优化：固定布局，避免重排 */
  contain: layout;
  width: 1112px; /* 明确设置宽度，匹配父容器 */
  box-sizing: border-box;
}

.file-upload-container, .config-panel {
  /* 强制固定宽度，防止任何变化 */
  width: 544px;
  min-width: 544px;
  max-width: 544px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fdfdfd;
  box-sizing: border-box;
}

.file-upload-container h4, .config-panel h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 文件上传 */
.dropzone {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}
.dropzone.active, .dropzone:hover {
  border-color: #007bff;
}
.file-name {
  margin-top: 10px;
  font-weight: 500;
  color: #007bff;
}
.error-message {
  margin-top: 10px;
  color: #e74c3c;
  font-weight: 500;
}

/* 全局配置 */
.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.config-item label {
  width: 120px;
  font-weight: 500;
  color: #34495e;
}
.config-item input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* === 控制按钮 === */
.batch-controls {
  text-align: center;
  /* 移除 margin-bottom，现在由 Grid gap 控制间距 */
}
.batch-controls button {
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}
.batch-controls button:hover {
  background-color: #0056b3;
}
.batch-controls button:disabled {
  background-color: #a0c7e4;
  cursor: not-allowed;
}

/* === 动态内容区域 === */
.batch-dynamic-content {
  /* 新增：包装进度条和结果的容器 */
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto; /* 允许滚动 */
  min-height: 0; /* 允许 Grid 项目收缩 */
  width: 1112px; /* 明确设置宽度，匹配父容器和配置区域 */
  max-width: 1112px; /* 防止溢出 */
  box-sizing: border-box;
}

/* === 进度和结果区域 === */
.batch-processing-area, .batch-results-area {
  /* 移除 margin-top，现在由父容器的 gap 控制间距 */
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  /* 防止内容溢出影响布局 */
  overflow: hidden;
}

/* 进度条 */
.batch-progress-container h4 {
  margin-top: 0;
}
.progress-bar-container {
  position: relative; /* 为绝对定位的文字提供参考 */
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin: 10px 0;
}
.progress-bar {
  height: 100%;
  background-color: #28a745;
  transition: width 0.4s ease;
  border-radius: 4px; /* 保持圆角 */
}
.progress-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333; /* 使用深色文字，确保在浅色背景上可见 */
  font-weight: 500;
  font-size: 12px;
  pointer-events: none; /* 防止文字阻挡点击事件 */
}
.progress-stats {
  display: flex;
  gap: 20px;
  font-weight: 500;
}
.success-text { color: #28a745; }
.failed-text { color: #dc3545; }
.retry-text { color: #f39c12; }

/* 结果列表 */
.batch-results-container h4 {
  margin-top: 0;
}
.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.result-row {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}
.result-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  background-color: #f8f9fa;
}
.result-summary:hover {
  background-color: #f1f3f5;
}
.summary-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-grow: 1; /* 允许其占据可用空间 */
  flex-shrink: 1; /* 允许其收缩 */
  min-width: 0; /* 关键属性，使得内部元素的 text-overflow 生效 */
}

.summary-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0; /* 防止右侧元素被压缩 */
}

.qa-block {
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许此块收缩 */
}

.status-icon { font-size: 1.2rem; }

.question, .answer {
  margin: 0;
  white-space: nowrap; /* 保持单行显示 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 使用省略号表示溢出 */
}
.expand-btn {
  padding: 4px 8px;
  font-size: 0.8rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
}
.result-details {
  padding: 12px;
  background-color: #fff;
  border-top: 1px solid #e9ecef;
}
.result-details pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #2c3e50;
  color: #f8f8f2;
  padding: 10px;
  border-radius: 4px;
}

/* 结果摘要右侧元素样式 */
.confidence-score {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.retry-info {
  font-size: 11px;
  color: #f39c12;
  background-color: #fef9e7;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 8px;
  border: 1px solid #f1c40f;
  white-space: nowrap;
}

/* === 调试辅助样式（可选，用于调试时查看布局边界） === */
/*
.batch-test-container { outline: 2px solid red; }
.batch-content { outline: 2px solid blue; }
.batch-config-area { outline: 2px solid green; }
.file-upload-container { outline: 1px solid orange; }
.config-panel { outline: 1px solid purple; }
.batch-dynamic-content { outline: 2px solid cyan; }
*/

/* === 响应式设计 === */
@media (max-width: 1240px) {
  .batch-test-container {
    width: 100%;
    max-width: 100%;
    padding: 16px;
  }

  .batch-config-area {
    grid-template-columns: 1fr 1fr; /* 保持两列但使用比例 */
  }

  .file-upload-container, .config-panel {
    width: auto; /* 允许自适应 */
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .batch-test-container {
    padding: 16px;
    width: 100%;
  }

  .batch-content {
    padding: 16px;
    min-height: 400px; /* 移动端减少最小高度 */
  }

  .batch-config-area {
    /* 移动端改为单列布局 */
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .file-upload-container, .config-panel {
    width: auto;
    min-width: auto;
    max-width: none;
    padding: 16px;
  }

  .batch-header h2 {
    font-size: 1.5rem;
  }

  .batch-header p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .batch-test-container {
    padding: 12px;
  }

  .batch-content {
    padding: 12px;
    gap: 16px;
  }

  .batch-config-area {
    gap: 12px;
    padding-bottom: 16px;
  }

  .file-upload-container, .config-panel {
    padding: 12px;
  }

  .config-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .config-item label {
    width: auto;
    margin-bottom: 4px;
  }
}