# AI智能客服前端应用

基于 React 19.1.0 构建的现代化AI智能客服系统前端，集成了智能对话、批量测试、FAQ录入等完整功能模块。

## 项目特性

### 🤖 核心功能
- **智能问答** - 支持多轮对话的AI智能客服系统，集成多个AI平台
- **候选答案** - 显示多个候选答案供用户选择，包含置信度分数和推理过程
- **会话管理** - 支持会话ID追踪和使用统计信息
- **多平台支持** - 支持百炼、火山方舟、Coze、Google等不同平台
- **实时反馈** - 显示请求耗时、加载状态和错误处理

### 📊 批量测试
- **文件上传** - 支持 Excel 和 JSON 格式的测试用例上传
- **批量执行** - 大规模FAQ测试用例并发执行
- **结果分析** - 详细的测试结果统计和相似度计算
- **黄金测试集** - 内置相似度计算的测试集验证功能

### 📝 FAQ录入
- **智能分析** - 基于LLM的问答内容分析和分类推荐
- **质量检查** - 自动检测重复问题和分类冲突
- **分类选择** - 可视化的分类树选择器
- **实时预览** - 录入内容的实时预览和验证

## 技术栈

### 核心框架
- **React 19.1.0** - 前端框架
- **React Router 7.6.2** - 路由管理
- **React Hooks** - 现代化的状态管理和副作用处理

### 工具库
- **Axios** - HTTP客户端
- **XLSX** - Excel文件处理
- **React Dropzone** - 文件拖拽上传
- **String Similarity** - 文本相似度计算
- **React Testing Library** - 完整的测试支持

### 开发工具
- **Create React App** - 官方脚手架工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化（配置中）

## 开发环境

### 前置要求
- **Node.js**: 推荐 16+ 版本
- **npm 或 yarn**: 包管理器
- **现代浏览器**: Chrome 80+, Firefox 75+, Safari 13+

### 安装依赖
```powershell
npm install
```

### 环境配置

#### 开发环境配置
复制 `.env.example` 文件为 `.env` 并根据需要修改：

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
# .env
REACT_APP_API_BASE_URL=http://localhost:8000/api/v1
```

#### Docker环境配置
Docker部署时会自动从环境变量生成配置，无需手动修改。

### 启动开发服务器
```bash
npm start
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 构建部署

### 生产构建
```bash
npm run build
```

构建文件将输出到 `build` 目录，包含优化后的静态资源。

### 测试
```bash
npm test
```

### 代码检查
```bash
npm run lint  # 如果配置了ESLint
```

## 项目结构

```
src/
├── App.js                           # 主应用组件
├── App.css                          # 主样式文件
├── config.js                        # 配置管理（支持运行时和构建时配置）
├── index.js                         # 应用入口
├── components/                      # React组件
│   ├── chat/                        # 聊天页面组件
│   │   ├── ChatPage.js             # 聊天页面主组件
│   │   ├── CandidateAnswers.js     # 候选答案展示
│   │   └── SessionInfo.js          # 会话信息显示
│   ├── batch/                       # 批量测试组件
│   │   ├── BatchTestPage.js        # 批量测试页面
│   │   ├── BatchProgress.js        # 进度显示
│   │   ├── BatchResults.js          # 结果展示
│   │   └── FileUpload.js           # 文件上传
│   ├── recorder/                    # FAQ录入组件
│   │   ├── FAQRecorderPage.js       # FAQ录入页面
│   │   ├── QuestionAnswerForm.js    # 问答表单
│   │   ├── CategorySelector.js     # 分类选择器
│   │   ├── AIRecommendations.js    # AI推荐结果
│   │   ├── QualityCheckResults.js  # 质量检查结果
│   │   └── SubmitConfirmation.js   # 提交确认
│   └── common/                      # 通用组件
│       ├── Navigation.js            # 导航栏
│       ├── ServiceSelector.js       # 服务选择器
│       └── ChannelSelector.js       # 渠道选择器
├── styles/                          # 样式文件
│   ├── App.css                      # 应用样式
│   ├── chat.css                     # 聊天页面样式
│   ├── batch.css                    # 批量测试样式
│   └── recorder.css                 # FAQ录入样式
└── utils/                           # 工具函数
    ├── fileParser.js                # 文件解析工具
    ├── requestId.js                 # 请求ID生成
    └── similarity.js                # 相似度计算

public/
├── config.js                        # 开发环境配置文件
├── index.html                       # HTML模板
├── favicon.ico                      # 网站图标
└── ...
```

## 主要功能模块

### 🗨️ 智能对话模块
- **多轮对话** - 完整的对话历史记录和上下文管理
- **实时响应** - 快速的AI回复和状态反馈
- **错误处理** - 完善的错误重试和用户提示机制
- **候选答案** - 多个答案选项，包含置信度评分

### 📈 批量测试模块
- **文件支持** - Excel (.xlsx) 和 JSON 格式测试用例
- **并发执行** - 支持多个测试用例同时执行
- **进度监控** - 实时显示测试进度和状态
- **结果分析** - 详细的成功率、响应时间统计
- **相似度计算** - 基于编辑距离的答案相似度评估

### 📝 FAQ录入模块
- **智能分析** - 使用LLM分析问答内容质量
- **分类推荐** - 基于现有FAQ结构的智能分类推荐
- **重复检测** - 自动检测与现有问答的相似性
- **质量评估** - 多维度的数据质量检查
- **可视化操作** - 直观的用户界面和操作流程

## API集成

应用通过以下端点与后端通信：

### 核心API
- `POST /api/v1/chat/faq_filter` - FAQ筛选对话接口
- `POST /api/v1/faq_recorder/analyze` - 问答分析接口
- `POST /api/v1/faq_recorder/categories` - 分类结构获取接口
- `POST /api/v1/faq_recorder/submit` - FAQ录入提交接口

### 请求格式
```javascript
// FAQ筛选请求
{
  "conversation": [
    {"role": "user", "content": "用户问题"},
    {"role": "assistant", "content": "AI回复"}
  ],
  "context": {
    "channel_name": "zulong",
    "platform": "volcano"
  }
}

// FAQ分析请求
{
  "qa_pair": {
    "question": "用户问题",
    "answer": "标准答案"
  },
  "service": "volcano"
}
```

## 配置管理说明

### 开发环境

#### 方式1（推荐）: 使用 `.env` 文件
- 复制 `.env.example` 为 `.env` 并修改配置
- 支持标准的 `REACT_APP_*` 环境变量
- 修改后重启开发服务器生效
- 配置文件不会被提交到版本控制

#### 方式2（备选）: 使用 `public/config.js` 文件
- 直接编辑 `public/config.js` 文件
- 修改后刷新页面即可生效
- 适合临时调试

### Docker环境
- 使用 `docker/frontend/.env` 文件配置
- 容器启动时动态生成 `config.js`
- 修改配置后重启容器即可生效，无需重新构建镜像

### 配置优先级
1. **运行时配置** (Docker环境): `window.APP_CONFIG`
2. **构建时配置** (开发环境): `process.env.REACT_APP_*` (来自.env文件)
3. **默认配置**: 硬编码的默认值

## 开发说明

### 组件开发规范
- **函数式组件** - 使用现代React函数式组件和Hooks
- **状态管理** - 使用 `useState` 和 `useEffect` 管理组件状态
- **样式管理** - CSS模块化，支持响应式设计
- **错误边界** - 实现错误边界处理，提升用户体验

### 代码组织原则
- **单一职责** - 每个组件只负责一个功能模块
- **可复用性** - 通用组件抽象到 `common/` 目录
- **可测试性** - 组件设计考虑测试需求
- **性能优化** - 使用 `React.memo`、`useCallback` 等优化手段

### 状态管理模式
- **本地状态** - 使用 `useState` 管理组件内部状态
- **全局状态** - 使用React Context或状态管理库（如需要）
- **服务状态** - 通过API响应和错误处理管理

### 样式系统
- **CSS模块** - 组件化样式管理
- **响应式设计** - 支持移动端和桌面端
- **主题系统** - 支持明暗主题切换（如需要）
- **动画效果** - 使用CSS动画提升用户体验

## 部署说明

### 开发环境部署
```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 3. 启动开发服务器
npm start
```

### 生产环境部署
```bash
# 1. 构建生产版本
npm run build

# 2. 部署构建产物
# 将 build/ 目录内容部署到Web服务器
```

### Docker部署
```bash
# 使用项目根目录的 docker/ 配置
cd ../docker
docker compose build frontend
docker compose up frontend
```

## 性能优化

### 代码分割
- 使用 `React.lazy` 和 `Suspense` 实现路由级代码分割
- 大型组件按需加载，减少初始包大小

### 缓存策略
- API响应数据缓存
- 静态资源缓存优化
- 浏览器本地存储利用

### 渲染优化
- 虚拟滚动（大数据列表）
- 防抖节流（频繁操作）
- 图片懒加载

## 测试策略

### 单元测试
- 使用 Jest 和 React Testing Library
- 组件渲染测试
- 用户交互测试
- 快照测试

### 集成测试
- API集成测试
- 路由导航测试
- 状态管理测试

### E2E测试
- 使用 Cypress 或 Playwright
- 完整用户流程测试
- 跨浏览器兼容性测试

## 故障排除

### 常见问题
1. **API连接失败** - 检查后端服务状态和API地址配置
2. **构建失败** - 检查Node.js版本和依赖安装情况
3. **样式问题** - 检查CSS导入和浏览器兼容性
4. **路由问题** - 确认React Router配置正确

### 调试工具
- **React Developer Tools** - 组件状态和props调试
- **浏览器开发者工具** - 网络请求和性能分析
- **Console日志** - 应用运行时日志信息

### 性能分析
- **Lighthouse** - 性能、可访问性、SEO分析
- **Webpack Bundle Analyzer** - 包大小分析
- **Chrome DevTools Performance** - 运行时性能分析

## 贡献指南

1. **代码规范** - 遵循项目ESLint配置
2. **提交规范** - 使用语义化提交信息
3. **测试要求** - 新功能需要包含相应测试
4. **文档更新** - 及时更新相关文档

## 版本历史

- **v1.2.0** - 添加FAQ录入功能，优化批量测试
- **v1.1.0** - 集成多AI平台支持，添加候选答案系统
- **v1.0.0** - 基础聊天功能，响应式设计