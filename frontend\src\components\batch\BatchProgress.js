import React from 'react';

function BatchProgress({ progress, stats }) {
  return (
    <div className="batch-progress-container">
      <h4>3. 测试进度</h4>
      <div className="progress-bar-container">
        <div
          className="progress-bar"
          style={{ width: `${progress}%` }}
        ></div>
        <div className="progress-text">
          {progress.toFixed(0)}%
        </div>
      </div>
      <div className="progress-stats">
        <span>总数: <strong>{stats.total}</strong></span>
        <span>已完成: <strong>{stats.completed}</strong></span>
        <span className="success-text">成功: <strong>{stats.success}</strong></span>
        <span className="failed-text">失败: <strong>{stats.failed}</strong></span>
        {stats.retried > 0 && (
          <span className="retry-text">重试: <strong>{stats.retried}</strong></span>
        )}
      </div>
    </div>
  );
}

export default BatchProgress;
