import React, { useState, useEffect, useMemo } from 'react';
import {
  generateNodeId,
  generateUUID,
  convertToUnifiedTree,
  findNodeInTree,
  findNodeByPath,
  addNodeToTree,
  removeNodeFromTree,
  updateNodeInTree,
  createAiVirtualNodes,
  mergeTreeWithAiVirtualNodes,
  extractManualVirtualNodes,
  mergeTreeWithManualVirtualNodes,
  isRecommendedPath,
  getRecommendationInfo,
  checkSiblingNameConflict,
  generateUniqueName,
  collectAllNodeIds,
  getFilteredTree
} from './CategorySelectorUtils';

/**
 * 混合式分类选择器组件
 * @param {Object} props
 * @param {Array} props.categoryTree - 完整的分类树结构
 * @param {Array} props.selectedPath - 当前选中的路径
 * @param {Function} props.onSelect - 路径选择回调
 * @param {string} props.channel - 当前渠道
 * @param {Array} props.recommendations - AI推荐结果，用于高亮显示
 */
function CategorySelector({ categoryTree, selectedPath, onSelect, channel, recommendations = [], onRefresh }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [unifiedTree, setUnifiedTree] = useState([]); // 统一的树结构，包含真实和虚拟节点
  const [editingNodeId, setEditingNodeId] = useState(null); // 正在编辑的节点ID
  const [refreshing, setRefreshing] = useState(false);

  // 使用useMemo计算AI虚拟节点，避免循环依赖
  const aiVirtualNodes = useMemo(() => {
    if (recommendations && recommendations.length > 0) {
      return createAiVirtualNodes(recommendations, categoryTree);
    }
    return [];
  }, [recommendations, categoryTree]);

  // 使用useMemo计算基础统一树结构（真实节点 + AI虚拟节点），避免循环依赖
  const baseUnifiedTree = useMemo(() => {
    const realTree = categoryTree && categoryTree.length > 0 ? convertToUnifiedTree(categoryTree) : [];
    return mergeTreeWithAiVirtualNodes(realTree, aiVirtualNodes);
  }, [categoryTree, aiVirtualNodes]);

  // 当基础统一树发生变化时，合并保留手动创建的虚拟节点
  useEffect(() => {
    setUnifiedTree(prevTree => {
      // 如果之前没有树结构，直接使用新的基础树
      if (!prevTree || prevTree.length === 0) {
        return baseUnifiedTree;
      }
      
      // 提取之前树中手动创建的虚拟节点
      const manualVirtualNodes = extractManualVirtualNodes(prevTree);
      
      // 合并基础树和手动虚拟节点
      return mergeTreeWithManualVirtualNodes(baseUnifiedTree, manualVirtualNodes);
    });
  }, [baseUnifiedTree]);

  // 获取过滤后的树（用于搜索）
  const filteredTree = useMemo(() => {
    return getFilteredTree(unifiedTree, searchTerm, setExpandedNodes);
  }, [unifiedTree, searchTerm]);

  // 切换节点展开状态
  const toggleExpanded = (nodeId) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  // 处理路径选择
  const handlePathSelect = (path) => {
    onSelect(path);
  };

  // 自动展开路径到目标节点并高亮
  const locateAndHighlightNode = (targetPath) => {
    // 清除搜索以确保节点可见
    setSearchTerm('');
    
    // 找到目标节点
    const targetNode = findNodeByPath(unifiedTree, targetPath);
    if (!targetNode) {
      console.warn('未找到目标节点:', targetPath);
      return;
    }
    
    // 展开从根到目标节点的所有父节点
    const nodesToExpand = new Set(expandedNodes);
    
    // 构建需要展开的路径
    for (let i = 1; i < targetPath.length; i++) {
      const parentPath = targetPath.slice(0, i);
      const parentNode = findNodeByPath(unifiedTree, parentPath);
      if (parentNode) {
        nodesToExpand.add(parentNode.id);
      }
    }
    
    setExpandedNodes(nodesToExpand);
    
    // 触发节点闪烁效果（纯CSS实现）
    setTimeout(() => {
      const nodeElement = document.querySelector(`[data-node-id="${targetNode.id}"]`);
      if (nodeElement) {
        const nodeContent = nodeElement.querySelector('.node-content');
        if (nodeContent) {
          // 移除之前的data-flash属性（如果存在）
          nodeContent.removeAttribute('data-flash');
          // 强制重绘后添加data-flash属性触发动画
          void nodeContent.offsetHeight; // 触发重绘
          nodeContent.setAttribute('data-flash', '');
        }
      }
    }, 150); // 稍微延迟以确保滚动完成
    
    // 滚动到目标节点（延迟执行以确保DOM更新完成）
    setTimeout(() => {
      const nodeElement = document.querySelector(`[data-node-id="${targetNode.id}"]`);
      const treeContainer = document.querySelector('.tree-container');
      
      if (nodeElement && treeContainer) {
        // 获取节点和容器的位置信息
        const nodeRect = nodeElement.getBoundingClientRect();
        const containerRect = treeContainer.getBoundingClientRect();
        
        // 计算节点相对于容器顶部的位置
        const relativeTop = nodeRect.top - containerRect.top + treeContainer.scrollTop;
        
        // 平滑滚动到目标位置，使节点显示在容器顶部
        treeContainer.scrollTo({
          top: relativeTop,
          behavior: 'smooth'
        });
      }
    }, 100);
  };

  // 处理分类链节点点击
  const handlePathSegmentClick = (index) => {
    const targetPath = selectedPath.slice(0, index + 1);
    locateAndHighlightNode(targetPath);
  };

  // 添加虚拟子节点
  const addVirtualChild = (parentPath) => {
    const newNodeId = generateUUID();
    
    // 生成唯一的节点名称，避免同级重名
    const uniqueName = generateUniqueName(unifiedTree, parentPath, '新分类');
    
    // 查找父节点以获取正确的parentId
    let parentId = null;
    if (parentPath.length > 0) {
      const parentNode = findNodeByPath(unifiedTree, parentPath);
      parentId = parentNode ? parentNode.id : generateNodeId(parentPath);
    }
    
    const newVirtualNode = {
      id: newNodeId,
      name: uniqueName,
      path: [...parentPath, uniqueName],
      children: [],
      answer_count: 0,
      isVirtual: true,
      parentId: parentId
    };
    
    setUnifiedTree(prev => addNodeToTree(prev, newVirtualNode, parentPath));
    
    // 自动展开父节点
    if (parentPath.length > 0) {
      const parentNode = findNodeByPath(unifiedTree, parentPath);
      const parentNodeId = parentNode ? parentNode.id : generateNodeId(parentPath);
      setExpandedNodes(prev => new Set([...prev, parentNodeId]));
    }
    
    // 自动进入编辑状态
    setEditingNodeId(newNodeId);
  };

  // 删除虚拟节点
  const removeVirtualNode = (nodeId) => {
    setUnifiedTree(prev => removeNodeFromTree(prev, nodeId));
    
    // 如果正在编辑该节点，取消编辑状态
    if (editingNodeId === nodeId) {
      setEditingNodeId(null);
    }
  };

  // 编辑虚拟节点名称
  const editVirtualNode = (nodeId, newName) => {
    if (!newName.trim()) {
      // 如果名称为空，取消编辑
      setEditingNodeId(null);
      return;
    }
    
    const node = findNodeInTree(unifiedTree, nodeId);
    if (!node) return;
    
    const parentPath = node.path.slice(0, -1);
    const trimmedName = newName.trim();
    
    // 检查同级是否有重名（排除自己）
    if (checkSiblingNameConflict(parentPath, trimmedName, nodeId)) {
      // 如果有重名，显示警告并保持编辑状态
      alert(`同级分类中已存在名为"${trimmedName}"的分类，请使用其他名称。`);
      return;
    }
    
    const newPath = [...parentPath, trimmedName];
    
    setUnifiedTree(prev => updateNodeInTree(prev, nodeId, {
      name: trimmedName,
      path: newPath
    }));
    
    setEditingNodeId(null);
  };

  // 展开全部节点
  const expandAllNodes = () => {
    const allNodeIds = collectAllNodeIds(filteredTree);
    setExpandedNodes(new Set(allNodeIds));
  };

  // 折叠全部节点
  const collapseAllNodes = () => {
    setExpandedNodes(new Set());
  };

  // 刷新分类数据
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // 清除编辑状态和搜索，但保留AI虚拟节点
      setEditingNodeId(null);
      onSelect([]);
      setSearchTerm('');
      setExpandedNodes(new Set());
      
      // 清除手动创建的虚拟节点，但保留AI推荐的虚拟节点
      // 重新构建树结构：真实节点 + AI虚拟节点
      const realTree = categoryTree && categoryTree.length > 0 ? convertToUnifiedTree(categoryTree) : [];
      const mergedTree = mergeTreeWithAiVirtualNodes(realTree, aiVirtualNodes);
      setUnifiedTree(mergedTree);
      
      // 如果有onRefresh回调，则调用它
      if (onRefresh) {
        await onRefresh();
      }
    } catch (error) {
      console.error('刷新分类数据失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 渲染树节点
  const renderTreeNode = (node, depth = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const isSelected = JSON.stringify(selectedPath) === JSON.stringify(node.path);
    const isRecommended = isRecommendedPath(node.path, recommendations);
    const recommendationInfo = getRecommendationInfo(node.path, recommendations);
    const isVirtual = node.isVirtual;
    const isAiRecommended = node.isAiRecommended; // AI推荐的虚拟节点
    const isEditing = isVirtual && !isAiRecommended && editingNodeId === node.id; // AI推荐的节点不可编辑

    
    return (
      <div 
        key={node.id} 
        className="tree-node" 
        style={{ marginLeft: `${depth * 20}px` }}
        data-node-id={node.id}
      >
        <div className={`node-content ${isSelected ? 'selected' : ''} ${isRecommended ? 'recommended' : ''} ${isVirtual ? 'virtual' : ''} ${isAiRecommended ? 'ai-recommended' : ''}`}>
          <div className="node-left">
            {node.children && node.children.length > 0 ? (
              <button
                className={`expand-button ${isExpanded ? 'expanded' : ''}`}
                onClick={() => toggleExpanded(node.id)}
              >
                {isExpanded ? '📂' : '📁'}
              </button>
            ) : (
              <span className="node-icon">📂</span>
            )}
            
            {isEditing ? (
              <input
                type="text"
                className="node-name-editor"
                defaultValue={node.name}
                autoFocus
                onBlur={(e) => editVirtualNode(node.id, e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    editVirtualNode(node.id, e.target.value);
                  } else if (e.key === 'Escape') {
                    setEditingNodeId(null);
                  }
                }}
              />
            ) : (
              <span className="node-name">
                {isVirtual && <span className="virtual-marker">* </span>}
                {node.name}
              </span>
            )}
            
            {isRecommended && (
              <span className="recommendation-star">
                ⭐ {Math.round(recommendationInfo.confidence * 100)}%
              </span>
            )}
            
            {!isVirtual && (node.answer_count > 0 || (node.children && node.children.length > 0)) && (
              <span className="answer-count">({node.children ? node.children.length : 0}:{node.answer_count})</span>
            )}
          </div>

          <div className="node-actions">
            {/* 添加子分类按钮 - 所有节点都可以添加子节点 */}
            <button
              className="add-child-button"
              onClick={() => addVirtualChild(node.path)}
              title="添加子分类"
            >
              +
            </button>
            
            {/* 虚拟节点的特殊操作按钮 - 只对手动创建的虚拟节点显示 */}
            {isVirtual && !isAiRecommended && (
              <>
                {!isEditing && (
                  <button
                    className="edit-virtual-button"
                    onClick={() => setEditingNodeId(node.id)}
                    title="编辑名称"
                  >
                    ✏️
                  </button>
                )}
                <button
                  className="remove-virtual-button"
                  onClick={() => removeVirtualNode(node.id)}
                  title="删除虚拟分类"
                >
                  −
                </button>
              </>
            )}
            
            <button
              className={`select-button ${isSelected ? 'selected' : ''}`}
              onClick={() => handlePathSelect(node.path)}
            >
              {isSelected ? '✓ 已选择' : '选择'}
            </button>
          </div>
        </div>

        {/* 显示推荐理由 */}
        {isRecommended && recommendationInfo && (
          <div className="recommendation-reason">
            <small>💡 {recommendationInfo.reason}</small>
          </div>
        )}

        {/* 递归渲染子节点 */}
        {isExpanded && node.children && node.children.length > 0 && (
          <div className="node-children">
            {node.children.map(child => renderTreeNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="category-selector">
      {/* 顶部工具栏 */}
      <div className="toolbar">
        <div className="search-section">
          <div className="search-input-wrapper">
            <input
              type="text"
              className="search-input"
              placeholder="🔍 搜索现有分类..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button
                className="clear-search"
                onClick={() => setSearchTerm('')}
              >
                ✕
              </button>
            )}
          </div>
          
          {searchTerm && (
            <div className="search-results-info">
              找到 {filteredTree.length} 个匹配的分类
            </div>
          )}
        </div>
        
        <div className="refresh-section">
          <button 
            className={`refresh-button ${refreshing ? 'refreshing' : ''}`}
            onClick={handleRefresh}
            disabled={refreshing}
            title="刷新分类数据"
          >
            {refreshing ? '🔄' : '🔄'} {refreshing ? '刷新中...' : '刷新'}
          </button>
        </div>
      </div>

      {/* 当前选择路径 */}
      {selectedPath && selectedPath.length > 0 && (
        <div className="current-selection">
          <div className="selection-header">
            <span className="selection-label">当前选择:</span>
            <button 
              className="clear-selection"
              onClick={() => onSelect([])}
            >
              清除选择
            </button>
          </div>
          <div className="selection-path">
            {selectedPath.map((category, index) => (
              <React.Fragment key={index}>
                <span 
                  className="path-segment clickable" 
                  onClick={() => handlePathSegmentClick(index)}
                  title={`点击定位到: ${selectedPath.slice(0, index + 1).join(' › ')}`}
                >
                  {category}
                </span>
                {index < selectedPath.length - 1 && (
                  <span className="path-arrow">›</span>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}

      {/* 分类树 */}
      <div className="tree-container">
        {/* 树操作按钮区域 */}
        <div className="tree-actions">
          <button
            className="add-root-button"
            onClick={() => addVirtualChild([])}
            title="添加根级分类"
          >
            + 添加根分类
          </button>
          <button
            className="expand-all-button"
            onClick={expandAllNodes}
            title="展开所有分类节点"
          >
            📂 展开全部
          </button>
          <button
            className="collapse-all-button"
            onClick={collapseAllNodes}
            title="折叠所有分类节点"
          >
            📁 折叠全部
          </button>
        </div>
        
        {filteredTree && filteredTree.length > 0 ? (
          <div className="category-tree">
            {filteredTree.map(node => renderTreeNode(node))}
          </div>
        ) : searchTerm ? (
          <div className="empty-search">
            <div className="empty-icon">🔍</div>
            <p>未找到匹配 "{searchTerm}" 的分类</p>
            <button 
              className="clear-search-button"
              onClick={() => setSearchTerm('')}
            >
              清除搜索
            </button>
          </div>
        ) : (
          <div className="empty-tree">
            <div className="empty-icon">📁</div>
            <p>暂无分类数据</p>
            <small>请先选择渠道以加载对应的分类结构</small>
          </div>
        )}
      </div>

      {/* 操作说明 */}
      <div className="selector-help">
        <div className="help-title">💡 使用说明:</div>
        <ul className="help-list">
          <li>⭐ 标记的分类是AI智能推荐</li>
          <li>📂 展开的分类，📁 折叠的分类</li>
          <li>🔵 点击"+ 添加根分类"按钮可创建新的一级分类</li>
          <li>+ 点击任意节点旁的加号可添加子分类（支持多层嵌套）</li>
          <li>* 标记的是手动创建的虚拟分类，可编辑或删除</li>
          <li>🤖 图标标识的是AI推荐的新分类，会自动创建到分类树中</li>
          <li>✏️ 点击编辑按钮修改手动虚拟分类名称，− 点击删除手动虚拟分类</li>
          <li>🔍 使用搜索框快速定位分类，🔄 点击刷新保留AI推荐分类并清除手动分类</li>
          <li>选择最具体的分类路径以获得最佳效果</li>
        </ul>
      </div>
    </div>
  );
}

export default CategorySelector;