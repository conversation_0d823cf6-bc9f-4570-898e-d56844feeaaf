.channel-selector-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.channel-selector-wrapper label {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.channel-selector {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.channel-selector:hover {
  border-color: #9ca3af;
}

.channel-selector:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.channel-selector:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.channel-selector:disabled:hover {
  border-color: #d1d5db;
}