import asyncio
import json
from typing import List, Dict, Any, Tuple, Optional

import chromadb
import chromadb.utils.embedding_functions as embedding_functions
from chromadb.config import Settings

from ai_app.models.chat import ChatModelUsage
from ai_app.shared.exceptions import LLMAPIError, LLMResponseError, EmbeddingCollectionNotFoundError
from ai_app.agents.shared.llm_impl.embedding_impl import EmbeddingImpl
from ai_app.shared.request_tracing import get_traced_logger
from ai_app.shared.timer_utils import create_timer

logger = get_traced_logger(__name__)


class ChromaEmbeddingImpl(EmbeddingImpl):
    """与Chroma向量数据库交互的具体实现。"""

    def __init__(self, host: str, port: int, api_base: str, api_key: str, model: str, collection_name: str, channel: str):
        """初始化Chroma向量数据库客户端。

        Args:
            host: Chroma服务器主机名。
            port: Chroma服务器端口。
            api_base: Embedding模型API基础URL。
            api_key: Embedding模型API密钥。
            model: Embedding模型名称。
            collection_name: Collection名称。
            channel: 渠道标识。
        """
        self.host = host
        self.port = port
        self.api_base = api_base
        self.api_key = api_key
        self.model = model
        self.collection_name = collection_name
        self.channel = channel
        
        logger.debug(f"ChromaEmbeddingImpl initialized for channel: {channel}, collection: {collection_name}")

    def get_doc_name(self) -> str:
        """获取文档名称标识。"""
        return f"chroma.{self.collection_name}.{self.channel}"

    async def search_knowledge(self,
        query: str,
        top_n: int,
    ) -> Tuple[List[Dict[str, Any]], ChatModelUsage, Dict[str, Any]]:
        """调用Chroma向量数据库进行知识检索。

        Args:
            query: 检索文本。
            top_n: 检索结果数量，范围 [1, 100]。

        Returns:
            Tuple[List[Dict[str, Any]], ChatModelUsage, Dict[str, Any]]: 包含检索结果、token使用量和原始响应。

        Raises:
            LLMAPIError: 如果 API 调用失败。
            LLMResponseError: 如果 API 响应格式不正确。
            EmbeddingCollectionNotFoundError: 如果知识库不存在。
        """
        # 创建计时器
        timer = create_timer()
        
        try:
            client = await chromadb.AsyncHttpClient(
                host=self.host, 
                port=self.port,
                settings=Settings(anonymized_telemetry=False)
            )
            # 获取collection
            try:
                embed_fn = embedding_functions.OpenAIEmbeddingFunction(
                    model_name=self.model,
                    api_base=self.api_base,
                    api_key=self.api_key
                )
                embed_fn = embedding_functions.OllamaEmbeddingFunction(model_name=self.model)
                collection = await client.get_or_create_collection(
                    name=self.collection_name,
                    embedding_function=embed_fn,
                    configuration={
                        "hnsw": {
                            "space": "cosine",
                        }
                    },
                )
            except Exception as e:
                logger.error(f"Failed to get collection {self.collection_name}: {e}")
                raise EmbeddingCollectionNotFoundError(f"Cannot access collection {self.collection_name}: {e}")
            
            # 限制top_n范围
            top_n = min(max(top_n, 1), 100)
            
            # 执行查询，使用where条件筛选channel
            results = await collection.query(
                query_texts=[query],
                n_results=top_n,
                where={"channel": self.channel},
                include=["metadatas", "distances"]
            )

            return_list = self.convert_to_faq_format(results)
            
            logger.debug(f"Chroma query completed. Query: {query[:50]}..., Results: {len(return_list)}")
            
            # 获取耗时（毫秒）
            duration_ms = timer.get_duration_ms()
            
            # 创建使用量信息（Chroma本身不返回token使用量，这里创建一个占位符）
            usage = ChatModelUsage(
                model_id=self.model,
                input_tokens=0,
                output_tokens=0,
                duration_ms=duration_ms
            )
            
            return return_list, usage, results
            
        except EmbeddingCollectionNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error during Chroma search: {e}")
            raise LLMAPIError(f"Chroma search failed: {e}") from e

    def convert_to_faq_format(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """将Chroma查询结果转换为FAQ格式。

        Args:
            data: Chroma查询返回的原始结果。

        Returns:
            转换后的FAQ格式结果列表。
        """
        logger.debug(f'data:{data}')
        formatted_results = []
        
        if not data or not data.get('ids') or not data['ids'][0]:
            return formatted_results
        
        # Chroma返回的是嵌套列表格式
        ids = data['ids'][0]
        metadatas = data['metadatas'][0]
        distances = data['distances'][0]
        
        for i, doc_id in enumerate(ids):
            try:
                metadata = metadatas[i] if i < len(metadatas) else {}
                distance = distances[i] if i < len(distances) else 1.0
                
                category_key_path = metadata.get('category_key_path', '0')
                
                # 计算相似度分数 (距离越小，相似度越高)
                similarity_score = max(0.0, 1.0 - distance)
                
                result_item = {
                    'key_path': category_key_path,
                    'score': similarity_score,
                }
                
                formatted_results.append(result_item)
                
            except Exception as e:
                logger.warning(f"Error processing Chroma result item {i}: {e}")
                continue
        
        return formatted_results
