# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🔔 重要工作规则

**测试执行规则**：
- 测试、验证、运行等工作由用户来执行
- Claude只负责指导和等待用户的反馈
- 不再主动执行bash命令或运行测试脚本

## 开发命令

### 前端 (React)
```bash
cd frontend
npm install              # 安装依赖
npm start               # 启动开发服务器 (端口 3000)
npm run build           # 构建生产版本
npm test                # 运行测试
```

### 后端 (FastAPI + Python)
```bash
cd backend_host

# 环境设置 (需要 uv 包管理器)
uv venv                 # 创建虚拟环境
#source .venv/bin/activate  # 激活环境 (Linux/macOS)
.venv\Scripts\Activate.ps1   # 激活环境 (Windows PowerShell)

uv sync                 # 从 uv.lock 安装依赖

# 运行开发服务器
uv run python -m ai_app.server.main -v     # 启动并启用详细日志
uv run python -m ai_app.server.main --reload  # 启动并启用热重载
```

### Docker 部署
```bash
# 在 docker/ 目录下
docker compose build    # 构建所有服务
docker compose up -d    # 后台启动所有服务
docker compose down    # 停止并删除所有服务
```

## 架构概览

这是一个中英双语 AI 聊天机器人应用，采用 React 前端和 FastAPI 后端，后端作为多个 AI 平台（百炼、Coze、火山方舟）的代理服务。

### 核心组件

**前端结构：**
- `frontend/src/components/chat/` - 主要聊天界面组件
- `frontend/src/components/batch/` - 批量测试功能
- `frontend/src/components/recorder/` - 问答对录入功能
- `frontend/src/components/common/` - 共享 UI 组件（导航、服务选择器）
- 使用 React Router 在聊天和批量测试页面间导航

**后端结构：**
- `backend_host/src/ai_app/server/main.py` - FastAPI 应用入口，配置 CORS 和中间件
- `backend_host/src/ai_app/server/routers/` - API 路由处理器，处理聊天、FAQ 筛选和健康检查
- `backend_host/src/ai_app/agents/faq_filter_agent/` - 核心 AI 代理，实现 FAQ 筛选工作流
- `backend_host/src/ai_app/agents/faq_recorder_agent/` - FAQ 录入代理，实现智能问答录入工作流
- `backend_host/src/ai_app/models/` - Pydantic 数据验证模型
- `backend_host/src/ai_app/services/` - 服务封装集成（百炼、Coze、数据管理）


### FAQ 筛选代理架构

核心 AI 代理（`backend_host/src/ai_app/agents/faq_filter_agent/`）实现了复杂的 FAQ 筛选工作流：


1. **查询重写** - 将用户查询重写为更清晰的形式
2. **问题分类** - 使用 LLM + FAQ 分类法对查询进行分类
3. **向量检索** - 分类失败时的语义搜索回退机制
4. **答案检索** - 从结构化 FAQ 数据中获取答案
5. **重排序** - 可选的答案相关性重排序

**代理组件：**
- `agent.py` - 主协调器和工作流协调者
- `llm_clients.py` - 不同任务的 LLM 客户端抽象
- `../shared/llm_impl/` - 平台特定实现（火山、百炼、重排序）
- `../shared/prompts/` - 不同任务的 LLM 提示模板
- FAQ数据通过Excel文件管理，位于 `resources/faq_data/` 目录

### FAQ 录入代理架构 ✅ **MVP 90%+ 完成**

FAQ 录入代理（`backend_host/src/ai_app/agents/faq_recorder_agent/`）实现了智能录入工作流：


1. **问题分析** - 使用 LLM 分析用户输入的原始问答内容 ✅
2. **分类推荐** - 基于现有 FAQ 结构推荐最合适的分类路径 ✅
3. **重复检测** - 检测与现有问答的重复和冲突 ✅
4. **质量评估** - 评估答案完整性和格式规范性 ✅
5. **数据录入** - 将确认后的数据写入 Excel 文件 ✅

**代理组件：**
- `agent.py` - 主协调器和工作流管理
- `llm_clients.py` - LLM 客户端封装，支持多平台分析功能
- `rule_based_recommender.py` - 基于规则的分类推荐器
- `quality_checker.py` - 数据质量检测和验证
- `excel_writer.py` - Excel 文件操作和数据写入
- `../shared/excel_utils.py` - 共享Excel处理工具

## 配置

**环境文件：**
- `backend_host/.env` - 后端配置（API 密钥、端点）
- `frontend/.env` - 前端配置（API 基础 URL）
- 从 `.env.example` 文件复制并配置 API 凭证

**核心后端配置 (backend_host/src/ai_app/config.py)：**
- 多 AI 平台支持（百炼、火山、Coze）
- 语义检索的向量数据库配置
- FAQ 数据文件路径和提示模板路径
- 带请求追踪的日志配置

## 测试

**FAQ 代理测试：**
从项目根目录执行：
```powershell
cd backend_host

# 运行FAQ筛选代理测试
uv run python -m tests.agents.faq_filter_agent.test_faq_management

# 运行FAQ录入代理测试
uv run python -m tests.agents.faq_recorder_agent.test_faq_recorder
```

**数据管理：**
FAQ数据通过统一的仓库管理器自动管理：
- Excel文件：`backend_host/resources/faq_data/faq.xlsx`
- 应用启动时自动加载所有sheet数据
- 支持多渠道数据（第一张表为通用数据，后续表为渠道增量数据）
- 自动备份机制：数据修改时创建备份文件

## 开发注意事项

- **包管理**：后端使用 `uv` 管理 Python 依赖，Python 版本为 3.11
- **API 端点**：主要 API 前缀为 `/api/v1/`
- **CORS**：开发环境配置为允许所有来源（生产环境需调整）
- **请求追踪**：所有请求包含 `x-request-id` 头用于调试
- **多渠道支持**：FAQ 代理支持不同渠道（如祖龙专用 FAQ 数据）
- **优雅降级**：分类失败时自动启用向量检索
- **日志记录**：通过 CLI 参数可配置详细的日志级别