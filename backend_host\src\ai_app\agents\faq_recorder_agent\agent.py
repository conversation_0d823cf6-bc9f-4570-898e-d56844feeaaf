"""
FAQ录入代理主协调器

负责协调各个组件，完成完整的FAQ录入工作流。
"""

import json
import sys
from typing import Dict, Any, List, Optional
from datetime import datetime

from ai_app.models.faq_recorder import (
    FAQRecorderAnalyzeRequest,
    FAQRecorderAnalyzeResponse,
    FAQRecorderCategoriesRequest,
    FAQRecorderCategoriesResponse,
    FAQRecorderSubmitRequest,
    FAQRecorderSubmitResponse,
    CategoryTreeNode
)
from ai_app.models.chat import ChatModelUsages, ChatModelUsage
from ai_app.services.faq_management.faq_repo_manager import get_faq_repo_manager
from ai_app.agents.faq_recorder_agent.llm_clients import (
    FAQExamineClient,
    FAQCategoryRecommendClient,
    FAQContentEnhancementClient
)
from ai_app.agents.faq_recorder_agent.rule_based_recommender import FAQRuleBasedRecommender
from ai_app.agents.faq_recorder_agent.quality_checker import FAQQual<PERSON><PERSON><PERSON><PERSON>
from ai_app.agents.faq_recorder_agent.excel_writer import FAQExcelWriter
from ai_app.agents.shared.llm_impl.volcano_impl import VolcanoLLMImpl
from ai_app.agents.shared.llm_impl.bailian_impl import BailianLLMImpl
from ai_app.agents.shared.llm_impl.google_impl import GoogleLLMImpl
from ai_app.shared.exceptions import (
    FAQRecorderError,
    ConfigurationError,
    ExamineQAPairError,
    QualityCheckError,
    ExcelWriteError
)
from ai_app.config import config
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)


class FAQRecorderAgent:
    """
    FAQ录入代理主协调器
    
    功能：
    1. 分析问答对内容
    2. 推荐分类路径
    3. 执行质量检查
    4. 写入Excel文件
    """
    
    def __init__(self, model_platform: str = "volcano", channel: Optional[str] = None):
        """
        初始化FAQ录入代理
        
        Args:
            model_platform: LLM平台，支持"volcano"和"bailian"
            channel: 渠道名称，为空时使用通用数据
        """
        try:
            self.model_platform = model_platform
            self.channel = channel
            logger.info(f"Initializing FAQRecorderAgent with platform: {model_platform}, channel: {channel}")
            
            # 初始化FAQ仓库管理器和解析器
            self.faq_repo_manager = get_faq_repo_manager()
            self.faq_parser = self.faq_repo_manager.create_parser(channel=channel)
            
            # 初始化LLM实现
            api_key, api_base, model_id = config.get_model_config(model_platform)
            if model_platform == "volcano":
                self.llm_impl = VolcanoLLMImpl(api_key, api_base, model_id)
            elif model_platform == "bailian":
                self.llm_impl = BailianLLMImpl(api_key, api_base, model_id)
            elif model_platform == "google":
                self.llm_impl = GoogleLLMImpl(api_key, api_base, model_id)
            else:
                raise ConfigurationError(f"Unsupported model platform: {model_platform}")
            
            # 初始化各个组件
            self.examine_client = FAQExamineClient(self.llm_impl)
            self.recommendation_client = FAQCategoryRecommendClient(self.llm_impl)
            self.enhancement_client = FAQContentEnhancementClient(self.llm_impl)
            self.rule_based_recommender = FAQRuleBasedRecommender(self.faq_parser)
            self.quality_checker = FAQQualityChecker(self.faq_parser)
            self.excel_writer = FAQExcelWriter(config.faq_excel_path)
            
            logger.info("FAQRecorderAgent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize FAQRecorderAgent: {e}")
            raise ConfigurationError(f"Agent initialization failed: {e}") from e
    
    async def analyze_qa_pair(self, request: FAQRecorderAnalyzeRequest) -> FAQRecorderAnalyzeResponse:
        """
        分析问答对并提供分类推荐
        
        Args:
            request: 分析请求
            
        Returns:
            分析响应
        """
        try:
            logger.info(f"Starting QA pair analysis: {request.qa_pair.question[:50]}...")
            
            model_usages = []
            
            # 1. LLM分析问答内容
            examine_result = {}
            if True:
                try:
                    examine_result, examine_usage = await self.examine_client.examine_qa_pair(
                        request.qa_pair
                    )
                    if examine_usage:
                        model_usages.append(examine_usage)
                    logger.debug("LLM examine completed:%s", json.dumps(examine_result, indent=2, ensure_ascii=False))
                except Exception as e:
                    logger.warning(f"LLM examine failed, continuing with rule-based analysis: {e}")
                    examine_result = {
                        "topic_domain": "未识别",
                        "question_type": "一般询问",
                        "keywords": [],
                        "applicable_scenarios": [],
                        "category_suggestions": [],
                        "confidence": 0.3,
                        "reasoning": f"LLM分析失败: {str(e)}"
                    }
            
            # 2. 获取分类结构用于推荐
            faq_structure = self.faq_parser.get_category_structure_tree()
            
            # 3. LLM分类推荐
            llm_recommendations = []
            if True:
                try:
                    llm_recommendations, recommend_usage = await self.recommendation_client.recommend_categories(
                        request.qa_pair, faq_structure, max_recommendations=3
                    )
                    if recommend_usage:
                        model_usages.append(recommend_usage)
                    logger.debug(f"LLM recommendations: {len(llm_recommendations)}\n{json.dumps([(rec.category_path, rec.confidence) for rec in llm_recommendations], indent=2, ensure_ascii=False)}")
                except Exception as e:
                    logger.warning(f"LLM recommendations failed: {e}")
            
            # 4. 基于规则的分类推荐
            rule_recommendations = []
            if True:
                try:
                    rule_recommendations = self.rule_based_recommender.recommend_categories(
                        request.qa_pair, max_recommendations=3
                    )
                    logger.debug(f"Rule-based recommendations: {len(rule_recommendations)}\n{json.dumps([(rec.category_path, rec.confidence) for rec in rule_recommendations], indent=2, ensure_ascii=False)}")
                except Exception as e:
                    logger.warning(f"Rule-based recommendations failed: {e}")
            
            # 5. 合并和去重推荐结果
            all_recommendations = self._merge_recommendations(llm_recommendations, rule_recommendations)
            
            # 6. 质量检查
            quality_check = None
            if all_recommendations:
                try:
                    quality_check = self.quality_checker.check_quality(
                        request.qa_pair,
                        [rec.category_path for rec in all_recommendations],
                    )
                    logger.debug("Quality check completed")
                except Exception as e:
                    logger.warning(f"Quality check failed: {e}")
            
            # 7. 构建响应
            usages = ChatModelUsages(models=model_usages) if model_usages else None
            
            response = FAQRecorderAnalyzeResponse(
                response_code=200,
                response_text="分析完成",
                examine_result=examine_result,
                recommended_categories=all_recommendations,
                quality_check=quality_check,
                usages=usages
            )
            
            logger.info(f"QA pair analysis completed, {len(all_recommendations)} recommendations generated")
            return response
            
        except Exception as e:
            logger.error(f"Error during QA pair analysis: {e}")
            return FAQRecorderAnalyzeResponse(
                response_code=500,
                response_text=f"分析失败: {str(e)}",
                examine_result=None,
                recommended_categories=[],
                quality_check=None,
                usages=None
            )
    
    def get_categories_structure(self, request: FAQRecorderCategoriesRequest) -> FAQRecorderCategoriesResponse:
        """
        获取分类结构
        
        Args:
            request: 分类结构请求
            
        Returns:
            分类结构响应
        """
        try:
            logger.info(f"Getting categories structure for channel: {self.channel}")
            
            # 使用agent初始化时确定的解析器
            parser = self.faq_parser
            
            # 获取Markdown格式的分类结构
            markdown_structure = parser.get_category_structure_markdown(max_depth=request.max_depth)
            
            # 构建分类树结构
            category_tree = self._build_category_tree(parser.faq_data, request.max_depth)
            
            # 统计信息
            total_categories = self._count_categories(parser.faq_data)
            total_answers = self._count_answers(parser.faq_data)
            
            response = FAQRecorderCategoriesResponse(
                response_code=200,
                response_text="获取分类结构成功",
                category_tree=category_tree,
                markdown_structure=markdown_structure,
                total_categories=total_categories,
                total_answers=total_answers
            )
            
            logger.info(f"Categories structure retrieved: {total_categories} categories, {total_answers} answers")
            return response
            
        except Exception as e:
            logger.error(f"Error getting categories structure: {e}")
            return FAQRecorderCategoriesResponse(
                response_code=500,
                response_text=f"获取分类结构失败: {str(e)}",
                category_tree=[],
                markdown_structure="",
                total_categories=0,
                total_answers=0
            )
    
    async def submit_faq_entry(self, request: FAQRecorderSubmitRequest) -> FAQRecorderSubmitResponse:
        """
        提交FAQ录入
        
        Args:
            request: 提交请求
            
        Returns:
            提交响应
        """
        try:
            logger.info(f"Starting FAQ entry submission: {request.qa_pair.question[:50]}...")
            
            # 1. 最终质量检查
            quality_check = self.quality_checker.check_quality(
                request.qa_pair,
                [request.category_path],
            )
            
            # 2. 检查是否允许提交
            if not quality_check.is_valid:
                logger.warning("Quality check failed while submitting FAQ entry")
                return FAQRecorderSubmitResponse(
                    response_code=400,
                    response_text="质量检查未通过，请修正问题后重试",
                    submitted_data=None,
                    quality_check=quality_check,
                    excel_info=None
                )
            
            # 3. 写入Excel文件
            try:
                # 确定目标工作表
                sheet_name = self.channel
                
                # 执行写入
                write_result = self.excel_writer.write_faq_entry(
                    request.qa_pair,
                    request.category_path,
                    sheet_name=sheet_name,
                    create_backup=True
                )
                
                logger.info("FAQ entry written to Excel successfully")
                
            except ExcelWriteError as e:
                logger.error(f"Excel write failed: {e}")
                return FAQRecorderSubmitResponse(
                    response_code=500,
                    response_text=f"写入Excel失败: {str(e)}",
                    submitted_data=None,
                    quality_check=quality_check,
                    excel_info=None
                )
            
            # 4. 重新加载FAQ数据（可选，用于验证）
            try:
                self.faq_repo_manager.reload_data()
                logger.debug("FAQ data reloaded after submission")
            except Exception as e:
                logger.warning(f"Failed to reload FAQ data: {e}")
            
            # 5. 构建提交数据信息
            submitted_data = {
                "qa_pair": request.qa_pair.dict(),
                "category_path": request.category_path,
                "channel": self.channel,
                "submit_time": datetime.now().isoformat(),
                "quality_passed": quality_check.is_valid,
            }
            
            # 6. 获取Excel文件信息
            excel_info = self.excel_writer.get_excel_info()
            
            response = FAQRecorderSubmitResponse(
                response_code=200,
                response_text="FAQ录入成功",
                submitted_data=submitted_data,
                quality_check=quality_check,
                excel_info=excel_info
            )
            
            logger.info("FAQ entry submission completed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error during FAQ entry submission: {e}")
            return FAQRecorderSubmitResponse(
                response_code=500,
                response_text=f"提交失败: {str(e)}",
                submitted_data=None,
                quality_check=None,
                excel_info=None
            )
    
    def _merge_recommendations(self, llm_recs, rule_recs):
        """
        合并LLM推荐和基于规则的推荐
        
        Args:
            llm_recs: LLM推荐结果
            rule_recs: 规则推荐结果
            
        Returns:
            合并后的推荐结果
        """
        # 将分类路径转换为字符串作为键，避免重复
        merged_dict = {}
        
        # 添加LLM推荐（优先级更高）
        for rec in llm_recs:
            path_key = " > ".join(rec.category_path)
            merged_dict[path_key] = rec
        
        # 添加规则推荐（如果不重复）
        for rec in rule_recs:
            path_key = " > ".join(rec.category_path)
            if path_key not in merged_dict:
                merged_dict[path_key] = rec
            else:
                # 如果重复，取置信度更高的
                existing = merged_dict[path_key]
                if rec.confidence > existing.confidence:
                    merged_dict[path_key] = rec
        
        # 转换为列表并按置信度排序
        result = list(merged_dict.values())
        result.sort(key=lambda x: x.confidence, reverse=True)
        
        return result[:8]  # 返回前8个推荐
    
    def _build_category_tree(self, faq_data: List[Dict], max_depth: int = -1) -> List[CategoryTreeNode]:
        """
        构建分类树结构
        
        Args:
            faq_data: FAQ数据
            max_depth: 最大深度
            
        Returns:
            分类树节点列表
        """
        def build_node(node_data: Dict, current_path: List[str], level: int) -> CategoryTreeNode:
            category_desc = node_data.get("category_desc", "")
            path = current_path + [category_desc]
            
            # 统计答案数量
            candidates = node_data.get("candidates", [])
            answer_count = len(candidates)
            has_answers = answer_count > 0
            
            # 处理子分类
            children = []
            if max_depth == -1 or level < max_depth:
                sub_categories = node_data.get("sub_category", [])
                for sub_node in sub_categories:
                    child = build_node(sub_node, path, level + 1)
                    children.append(child)
                    # 累加子分类的答案数量
                    answer_count += child.answer_count
                    if child.has_answers:
                        has_answers = True
            
            return CategoryTreeNode(
                name=category_desc,
                path=path,
                level=level,
                children=children,
                has_answers=has_answers,
                answer_count=answer_count
            )
        
        tree_nodes = []
        for root_node in faq_data:
            node = build_node(root_node, [], 0)
            tree_nodes.append(node)
        
        return tree_nodes
    
    def _count_categories(self, faq_data: List[Dict]) -> int:
        """
        统计分类数量
        
        Args:
            faq_data: FAQ数据
            
        Returns:
            分类总数
        """
        def count_recursive(nodes: List[Dict]) -> int:
            count = len(nodes)
            for node in nodes:
                sub_categories = node.get("sub_category", [])
                if sub_categories:
                    count += count_recursive(sub_categories)
            return count
        
        return count_recursive(faq_data)
    
    def _count_answers(self, faq_data: List[Dict]) -> int:
        """
        统计答案数量
        
        Args:
            faq_data: FAQ数据
            
        Returns:
            答案总数
        """
        def count_recursive(nodes: List[Dict]) -> int:
            count = 0
            for node in nodes:
                candidates = node.get("candidates", [])
                count += len(candidates)
                
                sub_categories = node.get("sub_category", [])
                if sub_categories:
                    count += count_recursive(sub_categories)
            
            return count
        
        return count_recursive(faq_data)
