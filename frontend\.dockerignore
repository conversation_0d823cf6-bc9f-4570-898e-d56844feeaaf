# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
build/
dist/

# 开发工具
.vscode/
.idea/
*.swp
*.swo

# 版本控制
.git/
.gitignore
.gitattributes

# 环境变量文件（保留.env.example）
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 测试相关
coverage/
.nyc_output/
*.test.js
*.spec.js
src/**/*.test.js
src/**/*.spec.js

# 日志文件
logs/
*.log

# 临时文件
.tmp/
.temp/
*.tmp
*.temp

# 操作系统文件
.DS_Store
Thumbs.db
desktop.ini

# 文档和说明
README.md
REFACTOR_PROGRESS.md
*.md

# 其他不需要的文件
.eslintcache
.stylelintcache
