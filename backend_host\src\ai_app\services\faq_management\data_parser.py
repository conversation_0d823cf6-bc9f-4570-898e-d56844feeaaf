import json
import os
from collections import deque
from typing import List, Dict, Any, Optional, Tuple # Added Tuple
from ai_app.shared.exceptions import FAQDataError # Import custom exception
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

# TODO: Consider adding custom exceptions from exceptions.py

class FAQDataParser:
    """负责加载、解析和查询 FAQ JSON 数据。"""

    def __init__(self, faq_data: List[Dict[str, Any]], channel: str):
        """初始化解析器，使用传入的 FAQ 数据。

        Args:
            faq_data: 直接传入的FAQ数据。
            channel: 渠道名称

        Raises:
            FAQDataError: 如果FAQ数据结构不正确。
        """
        if not isinstance(faq_data, list):
            raise FAQDataError("FAQ data structure is invalid: root element must be a list.")
        
        self.faq_data = faq_data
        self.channel = channel
        logger.debug(f"Successfully initialized FAQ parser with direct data of channel({self.channel}), {len(faq_data)} root categories")

    def get_category_structure_markdown(self, max_depth: int = -1) -> str:
        """生成 Markdown 格式的 FAQ 目录结构字符串，类似 test_faq_categories.md 格式。

        Args:
            max_depth: 最大递归层数。-1 表示无限制，正整数 n 表示最大生成 n 层目录。
                       例如，max_depth=1 只生成顶级目录，max_depth=2 生成顶级和二级目录。

        Returns:
            Markdown 格式的目录结构字符串。
        """
        markdown_str = ""
        
        # 使用迭代代替递归来处理层级
        categories_to_process = []
        for category in self.faq_data:
            categories_to_process.append((category, 0)) # (category_data, indent_level)

        while categories_to_process:
            category, indent_level = categories_to_process.pop(0) # FIFO for breadth-first like processing, or pop() for depth-first

            # 检查是否达到最大深度限制
            if max_depth != -1 and indent_level >= max_depth:
                logger.debug(f"Skipping category due to max_depth limit: {category.get('key_path')}")
                continue

            key_path = category.get('key_path', '?') # 直接使用 key_path
            desc = category.get('category_desc', 'N/A')
            indent = "  " * indent_level  # 两个空格的缩进

            markdown_str += f"{indent}{key_path} {desc}\n"

            sub_categories = category.get("sub_category")
            if isinstance(sub_categories, list) and sub_categories:
                # 将子类别添加到待处理列表的前面，以实现深度优先遍历的顺序
                # （或者添加到末尾实现广度优先，但为了保持原递归的顺序，这里用插入到前面）
                for sub_category in reversed(sub_categories): # reversed to maintain original order after pop(0)
                     categories_to_process.insert(0, (sub_category, indent_level + 1))
        
        logger.debug(f"Generated category structure markdown of channel({self.channel}) with max_depth={max_depth}")
        return markdown_str

    def get_category_structure_tree(self, max_depth: int = -1) -> str:
        """生成类似 bash tree 命令格式的 FAQ 目录结构字符串。

        Args:
            max_depth: 最大递归层数。-1 表示无限制，正整数 n 表示最大生成 n 层目录。
                       例如，max_depth=1 只生成顶级目录，max_depth=2 生成顶级和二级目录。

        Returns:
            类似 tree 命令格式的目录结构字符串，每个节点包含 category_desc。
        """
        tree_str = ""
        
        # 使用迭代代替递归来处理层级
        # 每个元素包含：(category_data, indent_level, is_last_in_parent, parent_prefixes)
        categories_to_process = []
        
        # 初始化根级别的分类
        for i, category in enumerate(self.faq_data):
            is_last = (i == len(self.faq_data) - 1)
            categories_to_process.append((category, 0, is_last, []))

        while categories_to_process:
            category, indent_level, is_last, parent_prefixes = categories_to_process.pop(0)

            # 检查是否达到最大深度限制
            if max_depth != -1 and indent_level >= max_depth:
                logger.debug(f"Skipping category due to max_depth limit: {category.get('key_path')}")
                continue

            desc = category.get('category_desc', 'N/A')
            
            # 构建当前行的前缀
            prefix = ""
            for parent_prefix in parent_prefixes:
                prefix += parent_prefix
            
            # 添加当前节点的树形符号
            if indent_level == 0:
                # 根级别不需要树形符号
                tree_symbol = ""
            else:
                tree_symbol = "└── " if is_last else "├── "
            
            tree_str += f"{prefix}{tree_symbol}{desc}\n"

            # 处理子分类
            sub_categories = category.get("sub_category")
            if isinstance(sub_categories, list) and sub_categories:
                # 为子分类准备新的前缀
                new_parent_prefixes = parent_prefixes.copy()
                if indent_level > 0:  # 只有非根级别才需要添加前缀
                    if is_last:
                        new_parent_prefixes.append("    ")  # 4个空格
                    else:
                        new_parent_prefixes.append("│   ")  # 竖线+3个空格
                
                # 将子类别添加到待处理列表的前面，以实现深度优先遍历的顺序
                for i, sub_category in enumerate(reversed(sub_categories)):
                    # 因为是reversed，所以is_last的逻辑需要反转
                    original_index = len(sub_categories) - 1 - i
                    is_last_sub = (original_index == len(sub_categories) - 1)
                    categories_to_process.insert(0, (sub_category, indent_level + 1, is_last_sub, new_parent_prefixes))
        
        logger.debug(f"Generated category structure tree of channel({self.channel}) with max_depth={max_depth}")
        return tree_str
    
    @staticmethod
    def description_path_to_string(description_path: List[str]) -> str:
        return " >>> ".join(description_path)

    def get_answer_by_key_path(self, key_path: str) -> Tuple[Optional[str], Optional[str], Optional[List[str]]]:
        """根据答案键路径 (e.g., '1.1.2', '*******') 查找并返回对应的答案和描述路径。

        Args:
            key_path: 答案节点的键路径字符串，不以'.'结尾。

        Returns:
            一个字典，其中：
            - answer: 对应的答案字符串，如果找不到则为 None。
            - question_example: 该答案的示例问题，如果找不到答案则为 None。
            - key_path: 实际答案对应的键路径字符串，如果找不到答案则为 None。
            - desc_path: ['desc1', 'desc2'] 格式的描述路径列表，如果路径无效则为 None。
        """
        #logger.debug(f"Attempting to find answer of channel({self.channel}) for key path: {key_path}")

        # 输入验证
        if not key_path or not isinstance(key_path, str) or len(key_path) == 0:
            logger.error(f"Invalid key_path received: {key_path}")
            return None

        if key_path == "0":
            #logger.debug("Key path '0' received, indicating no specific category match.")
            return None

        # 答案节点不应以'.'结尾
        if key_path.endswith('.'):
            logger.error(f"Answer key_path should not end with '.': {key_path}")
            return None

        # 推导上一级分类节点的key_path
        # 例如：'*******' -> '1.3.1.'
        try:
            # 找到最后一个'.'的位置
            last_dot_index = key_path.rfind('.')
            # 截取到该点，包括该点后的'.'
            category_key_path = key_path[:last_dot_index + 1]
        except Exception as e:
            logger.error(f"Error deriving category key path from '{key_path}': {e}")
            return None
        
        #logger.debug(f"Derived category key path: {category_key_path}")

        # 使用get_answers_by_key_path获取分类下的所有答案
        try:
            all_answers = self.get_answers_by_key_path(category_key_path)
        except Exception as e:
            logger.error(f"Error getting answers for category '{category_key_path}': {e}")
            return None

        # 在答案列表中查找匹配的key_path
        for answer_data in all_answers:
            if answer_data.get('key_path') == key_path:
                #logger.debug(f"Found answer of channel({self.channel}) for key path '{key_path}'")
                return answer_data

        # 找不到匹配的答案
        logger.error(f"No answer found for key path '{key_path}' in category '{category_key_path}'")
        return None

    def get_answers_by_key_path(self, key_path: str) -> List[Dict[str, Any]]:
        """根据分类键路径收集该分类及其子分类下的所有答案。

        Args:
            key_path: 分类键路径字符串，必须以'.'结尾 (e.g., '1.1.', '1.3.')。

        Returns:
            答案列表，每个答案包含：
            - answer: 答案内容
            - question_example: 问题示例
            - key_path: 答案的完整key_path
            - desc_path: 描述路径列表
        """
        #logger.debug(f"Attempting to collect all answers of channel({self.channel}) for category key path: {key_path}")

        # 输入验证
        if not key_path or not isinstance(key_path, str) or len(key_path) == 0:
            logger.error(f"Invalid key_path received: {key_path}")
            return []

        if key_path == "0" or key_path == "0.":
            logger.debug("Key path '0' received, indicating no specific category match.")
            return []
        
        if not key_path.endswith('.'):
            logger.error(f"Key path must end with '.': {key_path}")
            return []

        # 验证key_path格式：应该是数字.数字.数字.的格式
        key_path_without_dot = key_path.rstrip('.')
        if not key_path_without_dot:  # 只有'.'的情况
            logger.error(f"Invalid key_path format (only dots): {key_path}")
            return []

        # 检查是否为有效的数字路径格式
        try:
            segments = key_path_without_dot.split('.')
            for segment in segments:
                if not segment.isdigit():
                    logger.error(f"Invalid key_path format (non-numeric segment '{segment}'): {key_path}")
                    return []
        except Exception as e:
            logger.error(f"Error validating key_path format '{key_path}': {e}")
            return []

        # 找到目标分类节点
        try:
            target_node, target_desc_path = self._find_category_node(key_path)
            if target_node is None:
                logger.error(f"Category node not found for key path: {key_path}")
                return []
        except Exception as e:
            logger.error(f"Error finding category node for key path '{key_path}': {e}")
            return []

        # 递归收集所有答案
        try:
            all_answers = []
            self.collect_answers_recursively(all_answers, target_node, target_desc_path)

            #logger.debug(f"Collected {len(all_answers)} answers of channel({self.channel}) for category key path: {key_path}")
            return all_answers
        except Exception as e:
            logger.error(f"Error collecting answers for key path '{key_path}': {e}")
            return []

    def _find_category_node(self, key_path: str) -> Tuple[Optional[Dict[str, Any]], Optional[List[str]]]:
        """查找指定key_path对应的分类节点。

        Args:
            key_path: 分类键路径，以'.'结尾

        Returns:
            元组 (节点, 描述路径列表)，如果找不到则返回 (None, None)
        """
        current_nodes_list = self.faq_data
        found_path_descs: List[str] = []

        # 移除末尾的'.'来分割路径
        key_path_without_dot = key_path.rstrip('.')
        if not key_path_without_dot:  # 如果只有'.'，说明是根级别
            return None, None

        key_path_segments = key_path_without_dot.split('.')

        for i, _ in enumerate(key_path_segments):
            current_expected_full_key_path = ".".join(key_path_segments[0:i+1]) + "."
            matched_node_for_this_level: Optional[Dict[str, Any]] = None

            for node in current_nodes_list:
                if node.get('key_path') == current_expected_full_key_path:
                    matched_node_for_this_level = node
                    break

            if matched_node_for_this_level is None:
                logger.error(f"Path segment for '{current_expected_full_key_path}' not found in data structure while traversing for '{key_path}'.")
                return None, None

            found_path_descs.append(matched_node_for_this_level.get('category_desc', 'N/A'))

            if i == len(key_path_segments) - 1:
                # 到达目标节点
                return matched_node_for_this_level, found_path_descs
            else:
                # 继续到下一层级
                sub_categories = matched_node_for_this_level.get("sub_category")
                if isinstance(sub_categories, list) and sub_categories:
                    current_nodes_list = sub_categories
                else:
                    logger.error(f"Path '{key_path}' expects subcategories at '{current_expected_full_key_path}', but none exist or are not a list.")
                    return None, None

        return None, None

    def collect_answers_recursively(self, all_answers: List[Dict[str, Any]], node: Dict[str, Any] = None, desc_path: List[str] = None):
        """使用迭代方式收集节点及其子节点的所有答案。

        Args:
            node: 当前节点
            desc_path: 当前节点的描述路径
            all_answers: 用于收集答案的列表
        """
        # 使用队列进行广度优先遍历，避免递归调用
        # 队列元素格式：(节点, 对应的描述路径)

        if not node or not desc_path:
            # 如果参数为空，则将根节点及其desc_path加入队列
            queue = deque([(root_node, [root_node['category_desc']]) for root_node in self.faq_data])
        else:
            queue = deque([(node, desc_path)])

        while queue:
            # 从队列头部取出一个节点及其描述路径
            current_node, current_desc_path = queue.popleft()

            # 收集当前节点的candidates
            if "candidates" in current_node and isinstance(current_node["candidates"], list):
                for candidate in current_node["candidates"]:
                    answer_data = {
                        "answer": candidate.get("answer", ""),
                        "question_example": candidate.get("question_example", ""),
                        "key_path": candidate.get("key_path", ""),
                        "desc_path": current_desc_path.copy()  # 复制描述路径
                    }
                    all_answers.append(answer_data)

            # 将子分类节点加入队列，更新描述路径
            if "sub_category" in current_node and isinstance(current_node["sub_category"], list):
                for sub_node in current_node["sub_category"]:
                    # 为子节点构建新的描述路径：当前路径 + 子节点描述
                    sub_desc_path = current_desc_path + [sub_node.get("category_desc", "N/A")]
                    # 将子节点及其描述路径加入队列尾部
                    queue.append((sub_node, sub_desc_path))

    def get_key_path_from_desc_path(self, desc_path: List[str]) -> Optional[str]:
        """根据描述路径列表查找对应的分类节点键路径。

        描述路径表示分类节点，因此返回的键路径以"."结尾。
        - 例如：['账号', '手机邮箱绑定'] -> "1.3."

        Args:
            desc_path: 描述路径列表，例如 ['账号', '手机邮箱绑定']。

        Returns:
            对应的分类节点键路径字符串（以"."结尾），如果找不到则为 None。
        """
        #logger.debug(f"Attempting to find key path of channel({self.channel}) for description path: {desc_path}")

        if not desc_path or not isinstance(desc_path, list) or len(desc_path) == 0:
            logger.error(f"Invalid desc_path received: {desc_path}")
            return None

        current_nodes_list = self.faq_data

        for i, target_desc in enumerate(desc_path):
            matched_node_for_this_level: Optional[Dict[str, Any]] = None

            # 在当前层级中查找匹配的描述
            for node in current_nodes_list:
                if node.get('category_desc') == target_desc:
                    matched_node_for_this_level = node
                    break

            if matched_node_for_this_level is None:
                logger.error(f"Description '{target_desc}' not found at level {i} while traversing description path {desc_path}.")
                return None

            # 如果这是最后一个描述，返回找到节点的分类键路径（以"."结尾）
            if i == len(desc_path) - 1:
                node_key_path = matched_node_for_this_level.get('key_path')
                #logger.debug(f"Found complete category key path '{node_key_path}' of channel({self.channel}) for description path {desc_path}")
                return node_key_path
            else:
                # 继续到下一层级
                sub_categories = matched_node_for_this_level.get("sub_category")
                if isinstance(sub_categories, list) and sub_categories:
                    current_nodes_list = sub_categories
                else:
                    logger.error(f"Description path {desc_path} expects subcategories after '{target_desc}', but none exist or are not a list.")
                    return None

        # 这里不应该到达，因为循环中已经处理了所有情况
        logger.error(f"Unexpected end of traversal for description path {desc_path}")
        return None

    def validate_data_structure(self) -> Tuple[bool, List[str]]:
        """验证 FAQ 数据结构的完整性。

        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        def validate_categories(categories: List[Dict[str, Any]], path: str = "root") -> None:
            if not isinstance(categories, list):
                errors.append(f"{path}: categories should be a list")
                return
            
            for i, category in enumerate(categories):
                current_path = f"{path}[{i}]"
                
                # 检查必需字段
                if not isinstance(category, dict):
                    errors.append(f"{current_path}: category should be a dict")
                    continue
                
                if "category_desc" not in category:
                    errors.append(f"{current_path}: missing 'category_desc'")
                
                if "key_path" not in category:
                    errors.append(f"{current_path}: missing 'key_path'")
                
                # 验证 candidates
                if "candidates" in category:
                    candidates = category["candidates"]
                    if not isinstance(candidates, list):
                        errors.append(f"{current_path}.candidates: should be a list")
                    else:
                        for j, candidate in enumerate(candidates):
                            candidate_path = f"{current_path}.candidates[{j}]"
                            if not isinstance(candidate, dict):
                                errors.append(f"{candidate_path}: should be a dict")
                                continue
                            
                            if "answer" not in candidate:
                                errors.append(f"{candidate_path}: missing 'answer'")
                            
                            if "key_path" not in candidate:
                                errors.append(f"{candidate_path}: missing 'key_path'")
                
                # 递归验证子分类
                if "sub_category" in category:
                    validate_categories(category["sub_category"], f"{current_path}.sub_category")
        
        try:
            validate_categories(self.faq_data)
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return len(errors) == 0, errors

    def extract_all_category_paths(self) -> List[List[str]]:
        """
        提取所有现有的分类路径（迭代实现）
        
        Returns:
            分类路径列表，每个路径是字符串列表
        """
        category_paths = []
        
        # 使用队列进行迭代遍历，避免递归调用
        # 队列元素格式：(节点列表, 当前路径)
        from collections import deque
        queue = deque([(self.faq_data, [])])
        
        while queue:
            # 从队列头部取出节点列表和当前路径
            nodes, current_path = queue.popleft()
            
            for node in nodes:
                category_desc = node.get("category_desc", "")
                new_path = current_path + [category_desc]
                
                # 添加当前路径
                category_paths.append(new_path.copy())
                
                # 将子分类节点加入队列
                sub_categories = node.get("sub_category", [])
                if sub_categories:
                    queue.append((sub_categories, new_path))
        
        logger.debug(f"Extracted {len(category_paths)} category paths of channel({self.channel})")
        return category_paths
