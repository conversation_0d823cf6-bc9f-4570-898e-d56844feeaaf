#!/bin/sh
# 前端容器启动脚本
# 在容器启动时将环境变量注入到配置文件中

set -e

echo "正在生成前端运行时配置..."

# 使用envsubst将环境变量替换到配置模板中
envsubst < /usr/share/nginx/html/config.template.js > /usr/share/nginx/html/config.js

echo "前端配置文件生成完成:"
cat /usr/share/nginx/html/config.js

echo "正在生成Nginx配置..."

# 设置默认的后端URL（如果环境变量未设置）
export BACKEND_URL=${BACKEND_URL:-"http://backend_host:8000"}

# 使用envsubst将环境变量替换到nginx配置模板中
envsubst '${BACKEND_URL}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf

echo "Nginx配置文件生成完成，后端地址: ${BACKEND_URL}"

echo "启动Nginx服务器..."

# 启动nginx
exec nginx -g "daemon off;"
